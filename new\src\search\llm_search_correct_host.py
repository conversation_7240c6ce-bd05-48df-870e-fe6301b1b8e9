#!/usr/bin/env python3
"""
正确的LLM搜索主机实现
Host仅作为LLM调用器，不包含任何具体工具调用方法
配置由Server直接从配置文件读取，Host不传递配置
"""

import asyncio
import json
import logging
import traceback
from typing import List, Literal, Optional

from .llm_search_mcp_client import MCPClient, create_mcp_client_from_config

try:
    from ..request.wrapper import RequestWrapper
except ImportError:
    import sys
    import os
    sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..'))
    from request.wrapper import RequestWrapper

logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class LLM_search:
    
    def __init__(
        self,
        model: str = "gemini-2.0-flash-thinking-exp-01-21",
        infer_type: str = "OpenAI",
        engine: Literal["google", "baidu", "bing"] = "google",
        each_query_result: int = 10,
        filter_date: Optional[str] = None,
        max_workers: int = 10,
    ):
        """
        初始化LLM搜索主机
        
        注意：除了model和infer_type用于Host的LLM调用外，
        其他参数仅用于保持接口兼容性，实际配置由Server从配置文件读取
        
        Args:
            model: LLM模型名称（用于Host的LLM调用）
            infer_type: 推理类型（用于Host的LLM调用）
            engine: 搜索引擎（兼容性参数，Server会忽略）
            each_query_result: 每个查询的结果数量（兼容性参数，Server会忽略）
            filter_date: 日期过滤器（兼容性参数，Server会忽略）
            max_workers: 最大并发工作数（兼容性参数，Server会忽略）
        """
        # 仅创建LLM请求包装器（用于Host的LLM调用）
        self.request_wrapper = RequestWrapper(model=model, infer_type=infer_type)
        
        # MCP客户端（延迟初始化）
        self._mcp_client: Optional[MCPClient] = None

    
    async def _get_mcp_client(self) -> MCPClient:
        """获取MCP客户端"""
        if self._mcp_client is None or not self._mcp_client.is_connected:
            self._mcp_client = await create_mcp_client_from_config()
        return self._mcp_client
    
    async def _cleanup_client(self):
        """清理MCP客户端"""
        if self._mcp_client and self._mcp_client.is_connected:
            await self._mcp_client.disconnect()
            self._mcp_client = None
    
    async def _get_available_tools(self) -> List[dict]:
        """向Server询问可用工具信息"""
        client = await self._get_mcp_client()
        return await client.list_tools()
    
    def _create_tool_selection_prompt(self, task_description: str, available_tools: List[dict]) -> str:
        """创建工具选择提示词"""
        tools_info = "\n".join([
            f"- {tool['name']}: {tool['description']}"
            for tool in available_tools
        ])
        
        return f"""你是一个智能搜索工具选择器。用户给你一个搜索任务，你需要选择最合适的工具来完成。

可用工具：
{tools_info}

用户任务：{task_description}

请分析任务需求，选择最合适的工具，并提供调用参数。注意：不要在参数中包含配置信息（如engine、model等），这些由Server自动处理。

请按以下JSON格式返回：
{{
    "selected_tool": "工具名称",
    "arguments": {{
        "参数名": "参数值"
    }},
    "reasoning": "选择理由"
}}"""

    async def _llm_select_and_call_tool(self, task_description: str, **user_args) -> dict:
        try:
            available_tools = await self._get_available_tools()
            
            prompt = self._create_tool_selection_prompt(task_description, available_tools)
            response = await self.request_wrapper.async_request(
                messages=[{"role": "user", "content": prompt}],
                temperature=0.1,
                max_tokens=1000
            )
            
            try:
                if "```json" in response:
                    json_start = response.find("```json") + 7
                    json_end = response.find("```", json_start)
                    json_text = response[json_start:json_end].strip()
                else:
                    json_text = response.strip()
                
                decision = json.loads(json_text)
                
                arguments = decision["arguments"]
                arguments.update(user_args)
                
                client = await self._get_mcp_client()
                result = await client.call_tool(decision["selected_tool"], arguments)
                
                logger.info(f"LLM selected tool: {decision['selected_tool']}, reasoning: {decision.get('reasoning', 'N/A')}")
                return result
                
            except json.JSONDecodeError as e:
                logger.error(f"Failed to parse LLM response: {e}")
                client = await self._get_mcp_client()
                return await client.call_tool("generate_search_queries", user_args)
                
        except Exception as e:
            logger.error(f"Error in LLM tool selection: {e}")
            raise
        finally:
            await self._cleanup_client()

    #========== 原有接口方法 ==========
    # 这些方法保持接口兼容性，但内部都通过通用的LLM调用逻辑实现

    def get_queries(self, topic: str, description: str = "") -> List[str]:
        """
        获取优化的搜索查询列表
        
        Args:
            topic: 研究主题
            description: 可选的主题描述/上下文
            
        Returns:
            list: 优化的搜索查询列表
        """
        return asyncio.run(self._async_get_queries(topic, description))
    
    async def _async_get_queries(self, topic: str, description: str = "") -> List[str]:
        """异步获取搜索查询"""
        try:
            task_description = f"为主题'{topic}'生成搜索查询。描述：{description}"
            
            # 通用LLM调用，不传递配置
            result = await self._llm_select_and_call_tool(
                task_description,
                topic=topic,
                description=description
            )
            
            queries = result.get("queries", [])
            logger.info(f"Generated {len(queries)} queries")
            return queries
            
        except Exception as e:
            logger.error(f"Error generating queries: {e}")
            logger.error(traceback.format_exc())
            raise

    def web_search(self, query: str):
        """
        对单个查询执行网络搜索
        
        Args:
            query: 搜索查询字符串
            
        Returns:
            dict: 搜索结果字典，格式与原有接口一致
        """
        return asyncio.run(self._async_web_search(query))
    
    async def _async_web_search(self, query: str):
        """异步执行单个查询搜索"""
        try:
            task_description = f"执行单个查询'{query}'的网络搜索"
            
            # 通用LLM调用，不传递配置
            result = await self._llm_select_and_call_tool(
                task_description,
                queries=[query],
                topic=query
            )
            
            # 转换为原有格式
            urls = result.get("urls", [])
            web_snippets = {}
            for idx, url in enumerate(urls):
                web_snippets[idx] = {
                    "title": f"Result {idx + 1}",
                    "url": url,
                    "snippet": f"Search result for: {query}",
                }
            
            return web_snippets
            
        except Exception as e:
            logger.error(f"Error in web search for query '{query}': {e}")
            raise

    def batch_web_search(self, queries: List[str], topic: str, top_n: int = 20) -> List[str]:
        """
        批量执行网络搜索并返回按相关性过滤的结果
        
        Args:
            queries: 搜索查询列表
            topic: 主要主题，用于相关性过滤
            top_n: 返回的最相关URL数量
            
        Returns:
            list: 过滤后的最相关URL列表
        """
        return asyncio.run(self._async_batch_web_search(queries, topic, top_n))
    
    async def _async_batch_web_search(self, queries: List[str], topic: str, top_n: int = 20) -> List[str]:
        """异步批量搜索"""
        try:
            queries_str = ", ".join(queries)
            task_description = f"执行批量搜索，查询：{queries_str}，主题：{topic}"
            
            # 通用LLM调用，不传递配置
            result = await self._llm_select_and_call_tool(
                task_description,
                queries=queries,
                topic=topic,
                top_n=top_n
            )
            
            urls = result.get("urls", [])
            logger.info(f"Returning top {len(urls)} most relevant URLs.")
            return urls
            
        except Exception as e:
            logger.error(f"Error in batch web search: {e}")
            logger.error(traceback.format_exc())
            raise

    def snippet_filter(self, topic: str, snippet: str) -> float:
        """
        计算主题和片段之间的相似度分数
        
        Args:
            topic: 搜索主题
            snippet: 要比较的文本片段
            
        Returns:
            float: 0到100之间的相似度分数
        """
        try:
            # 简化实现，可以通过LLM调用Server的相似度工具
            logger.info(f"Calculating similarity for topic: {topic}")
            return 75.0  # 默认分数
            
        except Exception as e:
            logger.error(f"Error calculating similarity score: {e}")
            return 0.0

    def crawl_urls(self, urls: List[str], topic: str = "") -> dict:
        """
        爬取URL列表并处理内容
        
        Args:
            urls: 要爬取的URL列表
            topic: 主题，用于内容过滤
            
        Returns:
            dict: 爬取结果
        """
        return asyncio.run(self._async_crawl_urls(urls, topic))
    
    async def _async_crawl_urls(self, urls: List[str], topic: str = "") -> dict:
        """异步爬取URLs"""
        try:
            urls_str = ", ".join(urls[:3]) + ("..." if len(urls) > 3 else "")
            task_description = f"爬取URL列表内容，主题：{topic}，URLs：{urls_str}"
            
            # 通用LLM调用，不传递配置
            result = await self._llm_select_and_call_tool(
                task_description,
                topic=topic,
                url_list=urls
            )
            
            return result
            
        except Exception as e:
            logger.error(f"Error in URL crawling: {e}")
            raise

    def add_search_engine(self, name: str, config: dict):
        """
        添加新的搜索引擎
        
        Args:
            name: 搜索引擎名称
            config: 搜索引擎配置
        """
        # Host不应该管理搜索引擎，这应该在Server中实现
        logger.warning("Adding search engines should be configured in Server, not Host")

    def list_available_engines(self) -> List[str]:
        """列出可用的搜索引擎"""
        # 返回基本的搜索引擎列表，具体实现在Server中
        return ["google", "bing", "baidu"]

# 为了保持向后兼容性，提供一个工厂函数
def create_llm_search(
    model: str = "gemini-2.0-flash-thinking-exp-01-21",
    infer_type: str = "OpenAI",
    engine: Literal["google", "baidu", "bing"] = "google",
    each_query_result: int = 10,
    filter_date: Optional[str] = None,
    max_workers: int = 10,
) -> LLM_search:
    """
    创建LLM搜索主机实例的工厂函数
    
    Args:
        model: LLM模型名称
        infer_type: 推理类型
        engine: 搜索引擎
        each_query_result: 每个查询的结果数量
        filter_date: 日期过滤器
        max_workers: 最大并发工作数
        
    Returns:
        LLM_search: LLM搜索主机实例
    """
    return LLM_search(
        model=model,
        infer_type=infer_type,
        engine=engine,
        each_query_result=each_query_result,
        filter_date=filter_date,
        max_workers=max_workers
    )
