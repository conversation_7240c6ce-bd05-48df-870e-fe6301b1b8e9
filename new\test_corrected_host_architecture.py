#!/usr/bin/env python3
"""
测试修正后的Host架构
验证Host仅作为LLM调用器，通过Client向Server询问工具信息
"""

import asyncio
import json
import logging
import os
import sys

# 添加项目路径
sys.path.append(os.path.join(os.path.dirname(__file__), '.'))

from src.search.llm_search_mcp_client import MC<PERSON>lient, create_mcp_client_from_config
from src.search.llm_search_host import LLM_search

logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

async def test_client_server_communication():
    """测试Client-Server直接通信"""
    print("\n=== 测试Client-Server直接通信 ===")
    
    client = None
    try:
        # 1. 创建客户端
        client = await create_mcp_client_from_config()
        print(f"✓ 客户端连接状态: {client.is_connected}")
        
        # 2. 向Server询问工具信息
        tools = await client.list_tools()
        print(f"✓ Server提供的工具: {[tool['name'] for tool in tools]}")
        
        # 3. 直接调用Server工具
        result = await client.call_tool(
            "generate_search_queries",
            {
                "topic": "人工智能",
                "description": "研究人工智能的发展历程",
                "model": "gemini-2.0-flash-thinking-exp-01-21"
            }
        )
        
        print(f"✓ Server工具调用成功，生成了 {len(result['queries'])} 个查询")
        print(f"  查询示例: {result['queries'][:2]}")
        
        return True
        
    except Exception as e:
        print(f"✗ Client-Server通信测试失败: {e}")
        return False
    finally:
        if client:
            await client.disconnect()

def test_host_as_llm_caller():
    """测试Host作为LLM调用器"""
    print("\n=== 测试Host作为LLM调用器 ===")
    
    try:
        # 1. 创建Host实例
        llm_search = LLM_search(
            model="gemini-2.0-flash-thinking-exp-01-21",
            engine="google",
            each_query_result=5
        )
        print("✓ Host实例创建成功")
        print(f"  Host配置: {llm_search.search_config}")
        
        # 2. Host通过LLM选择工具并调用
        queries = llm_search.get_queries(
            topic="量子计算",
            description="研究量子计算的基本原理和应用"
        )
        
        print(f"✓ Host通过LLM调用成功，生成了 {len(queries)} 个查询")
        print(f"  查询示例: {queries[:2]}")
        
        return True
        
    except Exception as e:
        print(f"✗ Host测试失败: {e}")
        return False

async def test_host_internal_flow():
    """测试Host内部流程"""
    print("\n=== 测试Host内部流程 ===")
    
    try:
        print("1. 模拟Host向Client询问Server工具信息...")
        
        # 创建Client
        client = await create_mcp_client_from_config()
        
        # Host向Server询问工具信息
        available_tools = await client.list_tools()
        print(f"   ✓ Host获取到 {len(available_tools)} 个可用工具")
        
        # Host创建LLM提示词
        tools_info = "\n".join([
            f"- {tool['name']}: {tool['description']}"
            for tool in available_tools
        ])
        
        print("   ✓ Host创建了工具选择提示词")
        print(f"   工具信息预览: {tools_info[:100]}...")
        
        # 模拟LLM选择工具（这里简化为直接选择）
        selected_tool = "generate_search_queries"
        arguments = {
            "topic": "机器学习",
            "description": "研究机器学习算法",
            "model": "gemini-2.0-flash-thinking-exp-01-21"
        }
        
        print(f"   ✓ LLM选择了工具: {selected_tool}")
        
        # Host通过Client调用Server工具
        result = await client.call_tool(selected_tool, arguments)
        
        print(f"   ✓ 工具调用成功，生成了 {len(result['queries'])} 个查询")
        
        await client.disconnect()
        
        print("✓ Host内部流程测试成功")
        return True
        
    except Exception as e:
        print(f"✗ Host内部流程测试失败: {e}")
        return False

def print_correct_architecture():
    """打印正确的架构说明"""
    print("\n" + "="*70)
    print("修正后的MCP架构")
    print("="*70)
    print("""
🏠 Host (LLM调用器) - 修正后
├── 职责: 仅包含LLM调用逻辑和提示词
├── 功能: 
│   ├── 向Client询问Server的工具信息 ✅
│   ├── 使用LLM分析任务并选择合适的工具 ✅
│   ├── 通过Client调用Server工具 ✅
│   └── 保持与原有LLM_search接口一致 ✅
└── 不包含: 工具实现、搜索配置、业务逻辑 ✅

🔌 Client (通信桥梁) - 已正确
├── 职责: 纯粹的MCP协议通信
├── 功能:
│   ├── 连接管理和消息传递 ✅
│   ├── 向Server询问工具信息 ✅
│   ├── 调用Server工具 ✅
│   └── 返回Server响应 ✅
└── 不包含: 业务逻辑、工具选择、配置管理 ✅

🖥️ Server (工具+配置实现) - 已正确
├── 职责: 提供所有工具实现和配置
├── 功能:
│   ├── 实现具体的搜索工具 ✅
│   ├── 管理搜索引擎配置 ✅
│   ├── 处理LLM调用 ✅
│   └── 执行业务逻辑 ✅
└── 包含: 所有底层实现和配置 ✅

📊 正确的数据流向:
Host → Client → Server (询问工具信息)
Host → Client → Server (调用工具)
Server → Client → Host (返回结果)

🔄 正确的调用流程:
1. Host向Client询问Server可用工具
2. Host使用LLM分析任务并选择工具
3. Host通过Client调用Server工具
4. Server执行工具并返回结果
5. Host处理结果并返回给调用方
    """)

def print_key_corrections():
    """打印关键修正点"""
    print("\n" + "="*70)
    print("关键修正点")
    print("="*70)
    print("""
❌ 修正前的问题:
1. Host包含了工具信息和搜索配置
2. Host直接实现了业务逻辑
3. Host通过Agent间接调用Client
4. 架构层次不清晰，职责混乱

✅ 修正后的改进:
1. Host仅作为LLM调用器，不拥有工具信息 ✅
2. Host通过Client向Server询问工具信息 ✅
3. Host直接使用Client，无需Agent中介 ✅
4. 所有搜索配置和业务逻辑都在Server中 ✅
5. 清晰的职责分离：Host(LLM) → Client(通信) → Server(实现) ✅

🎯 核心原则:
• Host不应该知道有哪些工具，而是动态询问 ✅
• Host只负责LLM调用和工具选择逻辑 ✅
• Client只负责通信，不包含任何业务逻辑 ✅
• Server包含所有实际的工具实现和配置 ✅

💡 用户反馈的关键点:
"host不应该本身拥有被暴露的工具信息，而是通过向client发出询问，
client调用server得到工具信息后反馈，host仅仅是一个llm的调用，
应该仅仅包含提示词和llm选择调用方式" ✅ 已实现
    """)

def test_interface_compatibility():
    """测试接口兼容性"""
    print("\n=== 测试接口兼容性 ===")
    
    try:
        # 创建Host实例
        llm_search = LLM_search(
            model="gemini-2.0-flash-thinking-exp-01-21",
            engine="google",
            each_query_result=5
        )
        
        # 测试原有接口
        print("✓ 原有接口保持兼容:")
        print(f"  - get_queries() 方法存在: {hasattr(llm_search, 'get_queries')}")
        print(f"  - web_search() 方法存在: {hasattr(llm_search, 'web_search')}")
        print(f"  - batch_web_search() 方法存在: {hasattr(llm_search, 'batch_web_search')}")
        print(f"  - snippet_filter() 方法存在: {hasattr(llm_search, 'snippet_filter')}")
        print(f"  - crawl_urls() 方法存在: {hasattr(llm_search, 'crawl_urls')}")
        
        return True
        
    except Exception as e:
        print(f"✗ 接口兼容性测试失败: {e}")
        return False

async def main():
    """主测试函数"""
    print("开始测试修正后的Host架构...")
    
    # 测试各个组件
    client_success = await test_client_server_communication()
    host_success = test_host_as_llm_caller()
    flow_success = await test_host_internal_flow()
    interface_success = test_interface_compatibility()
    
    # 打印架构说明
    print_correct_architecture()
    print_key_corrections()
    
    # 总结
    print("\n" + "="*70)
    print("测试总结")
    print("="*70)
    
    if client_success and host_success and flow_success and interface_success:
        print("🎉 所有测试通过！修正后的Host架构实现成功")
        print("✓ Client-Server通信正常")
        print("✓ Host作为LLM调用器工作正常")
        print("✓ Host内部流程正确")
        print("✓ 接口兼容性保持")
        print("\n🎯 架构修正要点:")
        print("• Host不再拥有工具信息，而是动态询问Server")
        print("• Host仅包含LLM调用逻辑和提示词")
        print("• 所有配置和业务逻辑都在Server中实现")
        print("• 保持与原有LLM_search接口完全兼容")
    else:
        print("❌ 部分测试失败，请检查配置:")
        print(f"  Client-Server通信: {'✓' if client_success else '✗'}")
        print(f"  Host LLM调用: {'✓' if host_success else '✗'}")
        print(f"  Host内部流程: {'✓' if flow_success else '✗'}")
        print(f"  接口兼容性: {'✓' if interface_success else '✗'}")
    
    print("\n测试完成！")

if __name__ == "__main__":
    asyncio.run(main())
