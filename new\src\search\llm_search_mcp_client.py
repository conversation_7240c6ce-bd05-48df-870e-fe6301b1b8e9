import asyncio
import json
import logging
from typing import Any, Dict, List, Optional

from mcp import ClientSession
from mcp.client.stdio import stdio_client
from mcp.types import CallToolRequest, TextContent

logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class MCPClient:
    """
    通用MCP客户端 - 纯粹的通信桥梁
    只负责与MCP服务器的连接和通信，不包含任何业务逻辑
    """

    def __init__(self, server_config: Dict[str, Any]):
        """
        初始化MCP客户端

        Args:
            server_config: 服务器配置，包含command, args, env等
        """
        self.server_config = server_config
        self.session = None
        self.read_stream = None
        self.write_stream = None
        self._connected = False

    async def connect(self):
        """连接到MCP服务器"""
        try:
            if self._connected:
                logger.warning("Already connected to MCP server")
                return

            logger.info("Connecting to MCP Server...")

            params = {
                "command": self.server_config["command"],
                "args": self.server_config.get("args", []),
                "env": self.server_config.get("env", None)
            }

            self.read_stream, self.write_stream = await stdio_client(**params).__aenter__()
            self.session = await ClientSession(self.read_stream, self.write_stream).__aenter__()

            await self.session.initialize()
            self._connected = True

            logger.info("Successfully connected to MCP server")

        except Exception as e:
            logger.error(f"Failed to connect to MCP server: {e}")
            self._connected = False
            raise

    async def disconnect(self):
        """断开与MCP服务器的连接"""
        try:
            if not self._connected:
                return

            if self.session:
                await self.session.__aexit__(None, None, None)
            if self.read_stream and self.write_stream:
                await stdio_client(
                    command=self.server_config["command"],
                    args=self.server_config.get("args", []),
                    env=self.server_config.get("env", None)
                ).__aexit__(None, None, None)

            self._connected = False
            logger.info("Disconnected from MCP server")
        except Exception as e:
            logger.error(f"Error disconnecting from MCP server: {e}")

    async def list_tools(self) -> List[Dict[str, Any]]:
        """列出服务器可用的工具"""
        try:
            if not self._connected or not self.session:
                raise RuntimeError("Not connected to MCP server")

            tools = await self.session.list_tools()
            logger.debug(f"Available tools: {[tool.name for tool in tools.tools]}")

            return [
                {
                    "name": tool.name,
                    "description": tool.description,
                    "inputSchema": tool.inputSchema
                }
                for tool in tools.tools
            ]

        except Exception as e:
            logger.error(f"Error listing tools: {e}")
            raise

    async def call_tool(self, tool_name: str, arguments: Dict[str, Any]) -> Dict[str, Any]:
        """
        调用MCP工具

        Args:
            tool_name: 工具名称
            arguments: 工具参数

        Returns:
            dict: 工具执行结果
        """
        try:
            if not self._connected or not self.session:
                raise RuntimeError("Not connected to MCP server")

            logger.debug(f"Calling tool: {tool_name} with arguments: {arguments}")

            result = await self.session.call_tool(
                CallToolRequest(
                    name=tool_name,
                    arguments=arguments
                )
            )

            if result.content and len(result.content) > 0:
                content = result.content[0]
                if isinstance(content, TextContent):
                    return json.loads(content.text)

            raise ValueError(f"No valid response received from tool {tool_name}")

        except Exception as e:
            logger.error(f"Error calling tool {tool_name}: {e}")
            raise

    async def list_resources(self) -> List[Dict[str, Any]]:
        """列出服务器可用的资源"""
        try:
            if not self._connected or not self.session:
                raise RuntimeError("Not connected to MCP server")

            resources = await self.session.list_resources()
            logger.debug(f"Available resources: {[resource.uri for resource in resources.resources]}")

            return [
                {
                    "uri": resource.uri,
                    "name": resource.name,
                    "description": resource.description,
                    "mimeType": resource.mimeType
                }
                for resource in resources.resources
            ]

        except Exception as e:
            logger.error(f"Error listing resources: {e}")
            raise

    async def read_resource(self, uri: str) -> str:
        """读取资源内容"""
        try:
            if not self._connected or not self.session:
                raise RuntimeError("Not connected to MCP server")

            result = await self.session.read_resource(uri)
            return result.contents[0].text if result.contents else ""

        except Exception as e:
            logger.error(f"Error reading resource {uri}: {e}")
            raise

    @property
    def is_connected(self) -> bool:
        """检查是否已连接"""
        return self._connected

async def create_mcp_client(server_config: Dict[str, Any]) -> MCPClient:
    """
    创建并连接MCP客户端的工厂函数

    Args:
        server_config: 服务器配置

    Returns:
        MCPClient: 已连接的MCP客户端
    """
    try:
        client = MCPClient(server_config)
        await client.connect()
        return client

    except Exception as e:
        logger.error(f"Failed to create MCP client: {e}")
        raise

async def create_mcp_client_from_config(config_path: str = "config/llm_search_mcp_config.json",
                                       server_name: str = "llm_search_server") -> MCPClient:
    """
    从配置文件创建MCP客户端

    Args:
        config_path: 配置文件路径
        server_name: 服务器名称

    Returns:
        MCPClient: 已连接的MCP客户端
    """
    try:
        with open(config_path, 'r', encoding='utf-8') as f:
            config = json.load(f)

        server_config = config["servers"][server_name]
        return await create_mcp_client(server_config)

    except Exception as e:
        logger.error(f"Failed to create MCP client from config: {e}")
        raise

async def example_usage():
    """示例用法"""
    client = None
    try:
        # 创建客户端
        client = await create_mcp_client_from_config()

        # 列出可用工具
        tools = await client.list_tools()
        print("Available tools:", [tool["name"] for tool in tools])

        # 调用工具
        result = await client.call_tool(
            "generate_search_queries",
            {
                "topic": "machine learning optimization",
                "description": "Research on optimization techniques in machine learning"
            }
        )

        print("Tool result:")
        print(json.dumps(result, ensure_ascii=False, indent=2))

    except Exception as e:
        logger.error(f"Example usage failed: {e}")
    finally:
        if client:
            await client.disconnect()

if __name__ == "__main__":
    asyncio.run(example_usage())
