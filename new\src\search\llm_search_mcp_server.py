#!/usr/bin/env python3
"""
LLM Search MCP Server
基于LLM的智能搜索服务器
"""

import asyncio
import json
import logging
import sys
import os
from typing import Dict, Any, List

from mcp.server import Server
from mcp.types import Resource, Tool, TextContent
import mcp.server.stdio

# 检查可选依赖
try:
    from crawl4ai import AsyncWebCrawler, CacheMode, CrawlerRunConfig
    CRAWL4AI_AVAILABLE = True
except ImportError:
    CRAWL4AI_AVAILABLE = False
    AsyncWebCrawler = None
    CacheMode = None
    CrawlerRunConfig = None

try:
    from LLM_search import LLM_search
except ImportError:
    try:
        import sys
        sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..', 'LLMxMapReduce_V2', 'src'))
        from LLM_search import LLM_search
    except ImportError:
        LLM_search = None

try:
    from exceptions import AnalyseError
except ImportError:
    class AnalyseError(Exception):
        pass

try:
    from request import RequestWrapper
except ImportError:
    try:
        import sys
        sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..', 'LLMxMapReduce_V2'))
        from request import RequestWrapper
    except ImportError:
        RequestWrapper = None

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

app = Server("llm-search-server")

llm_search_instances = {}
@app.list_resources()
async def list_resources() -> List[Resource]:
    return [
        Resource(
            uri="llm://search/prompts",
            name="LLM Search Prompts",
            description="LLM搜索相关的提示词模板",
            mimeType="application/json"
        )
    ]

@app.read_resource()
async def read_resource(uri: str) -> str:
    if uri == "llm://search/prompts":
        prompts = {
            "query_generation": """
你是一个专业的搜索查询优化专家。请为给定的研究主题生成多个高质量的搜索查询。

## 输入信息
主题: {topic}
描述: {description}

## 查询生成原则
1. **多角度覆盖**: 从不同角度和层面生成查询
2. **关键词优化**: 使用精准的学术和专业术语
3. **查询多样性**: 包含不同类型的查询（概念性、具体性、比较性等）
4. **搜索引擎友好**: 优化查询以获得最佳搜索结果

## 输出格式
请按以下格式输出查询列表：
```json
{"查询1";"查询2";"查询3";"查询4";"查询5"}
```

请生成5-8个高质量的搜索查询。
""",
            "relevance_analysis": """
你是一个搜索结果相关性分析专家。请分析给定URL列表与研究主题的相关性。

## 分析任务
主题: {topic}
URL列表: {urls}

## 分析维度
1. **内容相关性**: URL内容与主题的匹配程度
2. **权威性**: 来源的可信度和专业性
3. **时效性**: 信息的新鲜度和时效性
4. **深度**: 内容的详细程度和深度

## 输出要求
请为每个URL提供相关性评分（0-100），并按相关性排序。
""",
            "page_refine": """
分析并处理以下与'{topic}'相关的网页内容。输出主体文本，去除图片链接，网址链接，广告，无意义重复字符等。禁止对内容进行总结，应保留所有与主题相关的信息。

原始网页内容：
{raw_content}

[输出要求]
- 标题：<TITLE>你的标题</TITLE>
- 过滤后文本：<CONTENT>过滤后文本</CONTENT>
""",
            "similarity_scoring": """
请你依据下列主题和在互联网上检索到的内容，判断这段内容的质量分数。

主题：{topic}
检索到的内容：{content}

请你依据以下几个维度，对这段检索到的内容进行打分。请你尽可能严格，批判性给分。

1. 内容与主题的相关程度。这需要考虑内容是否能被视为主题的一部分子内容进行展开。
2. 内容能够用于撰写与主题的文本的质量。这需要考虑文本的长度（例如：如果长度非常短，则用于参考的价值相对较低）、文本中是否包含较多乱码、文本本身的质量等。

请你综合考量上述两个维度，先给出评分的理由，再进行评分。你需要对每一个维度进行评分，评分范围是0-100。0表示完全不相关，100表示完全相关。完成每一个维度的评分后，你需要进行计算，得出最后的平均分。

注意，评分需要用<SCORE></SCORE>包裹起来。例如<SCORE>78</SCORE>

回答示例：
理由：...
相似度评分：<SCORE>89</SCORE>
"""
        }
        return json.dumps(prompts, ensure_ascii=False, indent=2)
    else:
        raise ValueError(f"Unknown resource: {uri}")

@app.list_tools()
async def list_tools() -> List[Tool]:
    return [
        Tool(
            name="generate_search_queries",
            description="基于LLM生成优化的搜索查询",
            inputSchema={
                "type": "object",
                "properties": {
                    "topic": {
                        "type": "string",
                        "description": "研究主题"
                    },
                    "description": {
                        "type": "string",
                        "description": "主题的可选描述或上下文"
                    },
                    "model": {
                        "type": "string",
                        "description": "用于查询生成的LLM模型",
                        "default": "gemini-2.0-flash-thinking-exp-01-21"
                    }
                },
                "required": ["topic"]
            }
        ),
        Tool(
            name="web_search",
            description="使用提供的查询执行网络搜索",
            inputSchema={
                "type": "object",
                "properties": {
                    "queries": {
                        "type": "array",
                        "items": {"type": "string"},
                        "description": "要执行的搜索查询列表"
                    },
                    "topic": {
                        "type": "string",
                        "description": "用于相关性过滤的主要主题"
                    },
                    "top_n": {
                        "type": "integer",
                        "description": "返回的最相关URL数量",
                        "default": 20
                    },
                    "engine": {
                        "type": "string",
                        "description": "使用的搜索引擎 (google, bing, baidu)",
                        "default": "google"
                    }
                },
                "required": ["queries", "topic"]
            }
        ),
        Tool(
            name="analyze_search_results",
            description="分析和过滤搜索结果的相关性",
            inputSchema={
                "type": "object",
                "properties": {
                    "urls": {
                        "type": "array",
                        "items": {"type": "string"},
                        "description": "要分析的URL列表"
                    },
                    "topic": {
                        "type": "string",
                        "description": "用于分析相关性的主题"
                    },
                    "max_results": {
                        "type": "integer",
                        "description": "返回的最大结果数量",
                        "default": 10
                    }
                },
                "required": ["urls", "topic"]
            }
        ),
        Tool(
            name="crawl_urls",
            description="异步爬取URL列表并处理内容：爬取 -> 内容过滤 -> 相似度评分 -> 结果排序",
            inputSchema={
                "type": "object",
                "properties": {
                    "topic": {
                        "type": "string",
                        "description": "研究主题，用于内容过滤和相似度评分"
                    },
                    "url_list": {
                        "type": "array",
                        "items": {"type": "string"},
                        "description": "要爬取的URL列表"
                    },
                    "top_n": {
                        "type": "integer",
                        "description": "返回的最高质量结果数量",
                        "default": 80
                    },
                    "model": {
                        "type": "string",
                        "description": "用于内容处理和相似度评分的LLM模型",
                        "default": "gemini-2.0-flash-thinking-exp-01-21"
                    },
                    "similarity_threshold": {
                        "type": "number",
                        "description": "相似度阈值 (0-100)",
                        "default": 80
                    },
                    "min_length": {
                        "type": "integer",
                        "description": "内容最小长度",
                        "default": 350
                    },
                    "max_length": {
                        "type": "integer",
                        "description": "内容最大长度",
                        "default": 20000
                    }
                },
                "required": ["topic", "url_list"]
            }
        )
    ]
@app.call_tool()
async def call_tool(name: str, arguments: Dict[str, Any]) -> List[TextContent]:
    """调用工具"""
    global llm_search_instances

    try:
        if name == "generate_search_queries":
            result = await _generate_search_queries(
                arguments["topic"],
                arguments.get("description", ""),
                arguments.get("model", "gemini-2.0-flash-thinking-exp-01-21")
            )
        elif name == "web_search":
            result = await _web_search(
                arguments["queries"],
                arguments["topic"],
                arguments.get("top_n", 20),
                arguments.get("engine", "google")
            )
        elif name == "analyze_search_results":
            result = await _analyze_search_results(
                arguments["urls"],
                arguments["topic"],
                arguments.get("max_results", 10)
            )
        elif name == "crawl_urls":
            result = await _crawl_urls(
                arguments["topic"],
                arguments["url_list"],
                arguments.get("top_n", 80),
                arguments.get("model", "gemini-2.0-flash-thinking-exp-01-21"),
                arguments.get("similarity_threshold", 80),
                arguments.get("min_length", 350),
                arguments.get("max_length", 20000)
            )
        else:
            raise ValueError(f"Unknown tool: {name}")

        return [TextContent(type="text", text=json.dumps(result, ensure_ascii=False, indent=2))]

    except Exception as e:
        logger.error(f"Error calling tool {name}: {e}")
        return [TextContent(type="text", text=f"Error: {str(e)}")]

def _get_llm_search_instance(model: str = "gemini-2.0-flash-thinking-exp-01-21",
                            engine: str = "google"):
    global llm_search_instances
    key = f"{model}_{engine}"
    if key not in llm_search_instances:
        llm_search_instances[key] = LLM_search(
            model=model,
            infer_type="OpenAI",
            engine=engine,
            each_query_result=10
        )
    return llm_search_instances[key]
async def _generate_search_queries(topic: str, description: str, model: str) -> Dict[str, Any]:
    """生成搜索查询"""
    logger.info(f"Generating search queries for topic: {topic}")

    try:
        llm_search = _get_llm_search_instance(model=model)
        queries = llm_search.get_queries(topic=topic, description=description)

        result = {
            "topic": topic,
            "description": description,
            "model": model,
            "queries": queries,
            "query_count": len(queries),
            "processing_metadata": {
                "model": model,
                "method": "llm_generation",
                "timestamp": "2025-01-23"
            }
        }

        return result

    except Exception as e:
        logger.error(f"Error generating queries: {e}")
        default_queries = [
            topic,
            f"{topic} research",
            f"{topic} analysis",
            f"{topic} study",
            f"{topic} overview"
        ]

        return {
            "topic": topic,
            "description": description,
            "model": model,
            "queries": default_queries,
            "query_count": len(default_queries),
            "processing_metadata": {
                "model": model,
                "method": "fallback_generation",
                "error": str(e)
            }
        }
async def _web_search(queries: List[str], topic: str, top_n: int, engine: str) -> Dict[str, Any]:
    logger.info(f"Performing web search for {len(queries)} queries")

    try:
        llm_search = _get_llm_search_instance(engine=engine)
        urls = llm_search.batch_web_search(queries=queries, topic=topic, top_n=top_n)

        result = {
            "topic": topic,
            "queries": queries,
            "engine": engine,
            "urls": urls,
            "url_count": len(urls),
            "top_n": top_n,
            "processing_metadata": {
                "engine": engine,
                "query_count": len(queries),
                "result_count": len(urls),
                "method": "batch_web_search"
            }
        }

        return result

    except Exception as e:
        logger.error(f"Error in web search: {e}")
        return {
            "topic": topic,
            "queries": queries,
            "engine": engine,
            "urls": [],
            "url_count": 0,
            "top_n": top_n,
            "processing_metadata": {
                "engine": engine,
                "query_count": len(queries),
                "result_count": 0,
                "method": "fallback",
                "error": str(e)
            }
        }
async def _analyze_search_results(urls: List[str], topic: str, max_results: int) -> Dict[str, Any]:
    """分析搜索结果"""
    logger.info(f"Analyzing {len(urls)} URLs for relevance to topic: {topic}")

    try:
        # 这里可以实现更复杂的分析逻辑
        # 目前简单返回前max_results个URL
        analyzed_urls = urls[:max_results]

        # 计算一些基本的分析指标
        analysis_scores = []
        for i, url in enumerate(analyzed_urls):
            # 简单的相关性评分
            score = max(0.5, 1.0 - (i * 0.1))  # 排名越靠前分数越高
            analysis_scores.append({
                "url": url,
                "relevance_score": score,
                "rank": i + 1
            })

        result = {
            "topic": topic,
            "original_count": len(urls),
            "analyzed_urls": analyzed_urls,
            "final_count": len(analyzed_urls),
            "analysis_method": "ranking_based_truncation",
            "analysis_scores": analysis_scores,
            "processing_metadata": {
                "input_count": len(urls),
                "output_count": len(analyzed_urls),
                "max_results": max_results,
                "analysis_method": "simple_ranking"
            }
        }

        return result

    except Exception as e:
        logger.error(f"Error analyzing search results: {e}")
        return {
            "topic": topic,
            "original_count": len(urls),
            "analyzed_urls": [],
            "final_count": 0,
            "analysis_method": "error_fallback",
            "analysis_scores": [],
            "processing_metadata": {
                "input_count": len(urls),
                "output_count": 0,
                "max_results": max_results,
                "error": str(e)
            }
        }
async def _crawl_urls(topic: str, url_list: List[str], top_n: int, model: str,
                     similarity_threshold: float, min_length: int, max_length: int) -> Dict[str, Any]:
    """异步爬取URL列表并处理内容"""
    logger.info(f"Starting crawling process for {len(url_list)} URLs with topic: {topic}")

    try:
        # 检查必要模块是否可用
        if not CRAWL4AI_AVAILABLE:
            raise ImportError("crawl4ai is not available")
        if RequestWrapper is None:
            raise ImportError("RequestWrapper is not available")

        import time
        import re
        request_wrapper = RequestWrapper(model=model, infer_type="OpenAI")

        process_start_time = time.time()
        stage_time = process_start_time

        # 爬取URL
        crawl_results = await _crawl_urls_stage(topic, url_list)
        logger.info(f"Stage 1 - Crawling completed in {time.time() - stage_time:.2f} seconds, with {len(crawl_results)} results")
        stage_time = time.time()

        # 内容过滤和标题生成
        filtered_results = await _process_filter_and_titles_stage(crawl_results, request_wrapper)
        logger.info(f"Stage 2 - Content filtering completed in {time.time() - stage_time:.2f} seconds, with {len(filtered_results)} results")
        stage_time = time.time()

        # 相似度评分
        scored_results = await _process_similarity_scores_stage(filtered_results, request_wrapper)
        logger.info(f"Stage 3 - Similarity scoring completed in {time.time() - stage_time:.2f} seconds, with {len(scored_results)} results")
        stage_time = time.time()

        # 结果处理和排序
        final_results = _process_and_sort_results(
            scored_results, top_n, similarity_threshold, min_length, max_length
        )
        logger.info(f"Stage 4 - Result processing completed in {time.time() - stage_time:.2f} seconds")

        total_time = time.time() - process_start_time
        logger.info(f"Total crawling process completed in {total_time:.2f} seconds")

        result = {
            "topic": topic,
            "total_urls": len(url_list),
            "crawl_results": len(crawl_results),
            "filtered_results": len(filtered_results),
            "scored_results": len(scored_results),
            "final_results": final_results,
            "final_count": len(final_results),
            "processing_metadata": {
                "model": model,
                "similarity_threshold": similarity_threshold,
                "min_length": min_length,
                "max_length": max_length,
                "top_n": top_n,
                "total_time": total_time,
                "success": True
            }
        }

        return result

    except Exception as e:
        logger.error(f"Error in crawling pipeline: {e}")
        return {
            "topic": topic,
            "total_urls": len(url_list),
            "crawl_results": 0,
            "filtered_results": 0,
            "scored_results": 0,
            "final_results": [],
            "final_count": 0,
            "processing_metadata": {
                "model": model,
                "similarity_threshold": similarity_threshold,
                "min_length": min_length,
                "max_length": max_length,
                "top_n": top_n,
                "success": False,
                "error": str(e)
            }
        }

async def _crawl_urls_stage(topic: str, url_list: List[str]) -> List[Dict[str, Any]]:
    """爬取URL阶段 - 使用生产者消费者模式"""
    import asyncio

    if not CRAWL4AI_AVAILABLE:
        raise ImportError("crawl4ai is not available")

    MAX_CONCURRENT_CRAWLS = 10
    input_queue = asyncio.Queue()
    output_queue = asyncio.Queue()
    total_items = len(url_list)

    # 添加URL到队列
    for url in url_list:
        await input_queue.put((url, topic))

    async def consumer():
        while True:
            try:
                url, topic = input_queue.get_nowait()
                try:
                    result = await _crawl_single_url(url, topic)
                    await output_queue.put(result)
                    logger.info(f"URL crawling completed, remaining: {input_queue.qsize()}/{total_items}, URL: {url}")
                finally:
                    input_queue.task_done()
            except asyncio.QueueEmpty:
                break

    consumers = [asyncio.create_task(consumer()) for _ in range(MAX_CONCURRENT_CRAWLS)]

    await input_queue.join()

    for consumer_task in consumers:
        consumer_task.cancel()

    results = []
    while not output_queue.empty():
        results.append(await output_queue.get())

    return results

async def _crawl_single_url(url: str, topic: str) -> Dict[str, Any]:
    try:
        if not CRAWL4AI_AVAILABLE:
            raise ImportError("crawl4ai is not available")

        crawler_run_config = CrawlerRunConfig(
            page_timeout=180000, cache_mode=CacheMode.BYPASS  # 180s timeout
        )

        async with AsyncWebCrawler() as crawler:
            result = await crawler.arun(url=url, config=crawler_run_config)
            raw_markdown = result.markdown_v2.raw_markdown
            logger.info(f"Content length={len(raw_markdown)} for URL={url}")

            return {
                "topic": topic,
                "url": url,
                "raw_content": raw_markdown,
                "error": False,
            }
    except Exception as e:
        logger.error(f"Crawling failed for URL={url}: {e}")
        return {
            "topic": topic,
            "url": url,
            "raw_content": f"Error: Crawling failed({e})",
            "error": True,
        }

async def _process_filter_and_titles_stage(crawl_results: List[Dict[str, Any]], request_wrapper) -> List[Dict[str, Any]]:
    import asyncio

    MAX_CONCURRENT_PROCESSES = 10
    input_queue = asyncio.Queue()
    output_queue = asyncio.Queue()

    for data in crawl_results:
        if not data.get("error", False):
            await input_queue.put(data)

    async def processor():
        while True:
            try:
                data = input_queue.get_nowait()
                try:
                    result = await _process_filter_and_title_single(data, request_wrapper)
                    await output_queue.put(result)
                finally:
                    input_queue.task_done()
            except asyncio.QueueEmpty:
                break

    processors = [asyncio.create_task(processor()) for _ in range(MAX_CONCURRENT_PROCESSES)]

    await input_queue.join()

    for processor_task in processors:
        processor_task.cancel()

    results = []
    while not output_queue.empty():
        results.append(await output_queue.get())

    return results

async def _process_filter_and_title_single(data: Dict[str, Any], request_wrapper) -> Dict[str, Any]:
    import re

    try:
        prompt_template = await read_resource("llm://search/prompts")
        prompts = json.loads(prompt_template)

        prompt = prompts["page_refine"].format(
            topic=data["topic"], raw_content=data["raw_content"]
        )
        res = request_wrapper.completion(prompt)
        title = re.search(r"<TITLE>(.*?)</TITLE>", res, re.DOTALL)
        content = re.search(r"<CONTENT>(.*?)</CONTENT>", res, re.DOTALL)

        if not title or not content:
            raise ValueError(f"Invalid response format, response: {res}")

        data["title"] = title.group(1).strip()
        data["content"] = content.group(1).strip()
        data["filter_error"] = False

        return data

    except Exception as e:
        logger.error(f"Content filtering failed for URL={data.get('url', 'unknown')}: {e}")
        data["title"] = f"Error processing: {data.get('url', 'unknown')}"
        data["content"] = f"Error: Content filtering failed({e})"
        data["filter_error"] = True
        return data

async def _process_similarity_scores_stage(filtered_results: List[Dict[str, Any]], request_wrapper) -> List[Dict[str, Any]]:

    import asyncio

    MAX_CONCURRENT_PROCESSES = 10
    input_queue = asyncio.Queue()
    output_queue = asyncio.Queue()

    for data in filtered_results:
        if not data.get("filter_error", False):
            await input_queue.put(data)

    async def scorer():
        while True:
            try:
                data = input_queue.get_nowait()
                try:
                    result = await _process_similarity_score_single(data, request_wrapper)
                    await output_queue.put(result)
                finally:
                    input_queue.task_done()
            except asyncio.QueueEmpty:
                break

    scorers = [asyncio.create_task(scorer()) for _ in range(MAX_CONCURRENT_PROCESSES)]

    await input_queue.join()

    for scorer_task in scorers:
        scorer_task.cancel()

    results = []
    while not output_queue.empty():
        results.append(await output_queue.get())

    return results

async def _process_similarity_score_single(data: Dict[str, Any], request_wrapper) -> Dict[str, Any]:
    import re

    try:
        prompt_template = await read_resource("llm://search/prompts")
        prompts = json.loads(prompt_template)
        
        prompt = prompts["similarity_scoring"].format(
            topic=data["topic"], content=data["content"]
        )
        res = request_wrapper.completion(prompt)
        score_match = re.search(r"<SCORE>(\d+)</SCORE>", res)

        if score_match:
            data["similarity_score"] = int(score_match.group(1))
        else:
            data["similarity_score"] = 50
            logger.warning(f"No score found in response for URL={data.get('url', 'unknown')}, using default score 50")

        data["score_error"] = False
        return data

    except Exception as e:
        logger.error(f"Similarity scoring failed for URL={data.get('url', 'unknown')}: {e}")
        data["similarity_score"] = 0
        data["score_error"] = True
        return data

def _process_and_sort_results(scored_results: List[Dict[str, Any]], top_n: int,
                             similarity_threshold: float, min_length: int, max_length: int) -> List[Dict[str, Any]]:

    filtered_results = []
    for result in scored_results:
        if (not result.get("score_error", False) and
            result.get("similarity_score", 0) >= similarity_threshold and
            min_length <= len(result.get("content", "")) <= max_length):
            filtered_results.append(result)

    filtered_results.sort(key=lambda x: x.get("similarity_score", 0), reverse=True)

    final_results = filtered_results[:top_n]

    formatted_results = []
    for i, result in enumerate(final_results):
        formatted_result = {
            "rank": i + 1,
            "url": result.get("url", ""),
            "title": result.get("title", ""),
            "content": result.get("content", ""),
            "similarity_score": result.get("similarity_score", 0),
            "content_length": len(result.get("content", "")),
            "topic": result.get("topic", "")
        }
        formatted_results.append(formatted_result)

    return formatted_results

async def main():
    logger.info("Starting LLM Search MCP Server...")
    async with mcp.server.stdio.stdio_server() as (read_stream, write_stream):
        await app.run(read_stream, write_stream, app.create_initialization_options())

if __name__ == "__main__":
    asyncio.run(main())
