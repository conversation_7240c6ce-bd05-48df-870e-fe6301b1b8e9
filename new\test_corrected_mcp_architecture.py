#!/usr/bin/env python3
"""
测试修正后的MCP架构
验证Server、Client、Host的正确分工和连接方式
"""

import asyncio
import json
import logging
import os
import sys

# 添加项目路径
sys.path.append(os.path.join(os.path.dirname(__file__), '.'))

from src.search.llm_search_mcp_client import MCPClient, create_mcp_client_from_config
from src.search.llm_search_agent import LLMConversationAgent
from src.search.llm_search_host import LLM_search

logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

async def test_mcp_client():
    """测试MCP客户端 - 纯通信桥梁"""
    print("\n=== 测试MCP客户端 ===")
    
    client = None
    try:
        # 创建客户端
        client = await create_mcp_client_from_config()
        print(f"✓ 客户端连接状态: {client.is_connected}")
        
        # 列出工具
        tools = await client.list_tools()
        print(f"✓ 可用工具: {[tool['name'] for tool in tools]}")
        
        # 调用工具
        result = await client.call_tool(
            "generate_search_queries",
            {
                "topic": "机器学习优化",
                "description": "研究机器学习中的优化技术"
            }
        )
        
        print(f"✓ 工具调用成功，生成了 {len(result['queries'])} 个查询")
        print(f"  查询示例: {result['queries'][:2]}")
        
    except Exception as e:
        print(f"✗ 客户端测试失败: {e}")
        logger.error(f"Client test error: {e}")
    finally:
        if client:
            await client.disconnect()

async def test_llm_agent():
    """测试LLM对话代理 - 智能协调器"""
    print("\n=== 测试LLM对话代理 ===")
    
    agent = None
    try:
        # 创建代理
        agent = LLMConversationAgent()
        
        # 执行搜索任务
        task_description = """生成搜索查询任务：
主题: 深度学习优化算法
描述: 研究深度学习中的优化算法，包括Adam、SGD等
模型: gemini-2.0-flash-thinking-exp-01-21
要求: 生成优化的搜索查询列表，用于学术研究

请使用generate_search_queries工具，参数：
- topic: 深度学习优化算法
- description: 研究深度学习中的优化算法，包括Adam、SGD等
- model: gemini-2.0-flash-thinking-exp-01-21"""
        
        result = await agent.execute_search_task(task_description)
        
        if result["success"]:
            print(f"✓ 代理任务执行成功")
            print(f"  LLM分析: {result['llm_analysis'][:100]}...")
            print(f"  选择工具: {result['selected_tool']}")
            print(f"  生成查询数: {len(result['tool_result']['queries'])}")
        else:
            print(f"✗ 代理任务失败: {result.get('error', 'Unknown error')}")
            
    except Exception as e:
        print(f"✗ 代理测试失败: {e}")
        logger.error(f"Agent test error: {e}")
    finally:
        if agent:
            await agent.cleanup()

def test_llm_search_host():
    """测试LLM搜索主机 - 接口适配器"""
    print("\n=== 测试LLM搜索主机 ===")
    
    try:
        # 创建主机
        llm_search = LLM_search(
            model="gemini-2.0-flash-thinking-exp-01-21",
            engine="google",
            each_query_result=5
        )
        
        # 测试查询生成
        queries = llm_search.get_queries(
            topic="自然语言处理",
            description="研究自然语言处理的最新进展"
        )
        
        print(f"✓ 主机查询生成成功，生成了 {len(queries)} 个查询")
        print(f"  查询示例: {queries[:2]}")
        
        # 测试批量搜索
        urls = llm_search.batch_web_search(
            queries=queries[:2],  # 只测试前2个查询
            topic="自然语言处理",
            top_n=5
        )
        
        print(f"✓ 主机批量搜索成功，返回了 {len(urls)} 个URL")
        print(f"  URL示例: {urls[:2]}")
        
    except Exception as e:
        print(f"✗ 主机测试失败: {e}")
        logger.error(f"Host test error: {e}")

async def test_architecture_integration():
    """测试整体架构集成"""
    print("\n=== 测试架构集成 ===")
    
    try:
        # 1. 直接使用Client（底层通信）
        print("1. 测试Client层...")
        client = await create_mcp_client_from_config()
        client_result = await client.call_tool(
            "generate_search_queries",
            {"topic": "区块链技术"}
        )
        await client.disconnect()
        print(f"   Client层成功: 生成 {len(client_result['queries'])} 个查询")
        
        # 2. 使用Agent（智能协调）
        print("2. 测试Agent层...")
        agent = LLMConversationAgent()
        agent_result = await agent.execute_search_task(
            "生成关于区块链技术的搜索查询"
        )
        await agent.cleanup()
        print(f"   Agent层成功: {agent_result['success']}")
        
        # 3. 使用Host（接口适配）
        print("3. 测试Host层...")
        host = LLM_search()
        host_queries = host.get_queries("区块链技术")
        print(f"   Host层成功: 生成 {len(host_queries)} 个查询")
        
        print("✓ 架构集成测试通过")
        
    except Exception as e:
        print(f"✗ 架构集成测试失败: {e}")
        logger.error(f"Integration test error: {e}")

def print_architecture_summary():
    """打印架构总结"""
    print("\n" + "="*60)
    print("MCP架构总结")
    print("="*60)
    print("""
📦 MCP Server (llm_search_mcp_server.py)
   ├── 功能: 提供具体的业务逻辑和工具实现
   ├── 职责: 实现搜索、爬取、分析等核心功能
   └── 特点: 独立运行，处理工具调用请求

🔌 MCP Client (llm_search_mcp_client.py) 
   ├── 功能: 纯粹的通信桥梁
   ├── 职责: 连接管理、消息传递、协议处理
   └── 特点: 不包含业务逻辑，通用化设计

🤖 LLM Agent (llm_search_agent.py)
   ├── 功能: 智能任务协调器
   ├── 职责: LLM对话、工具选择、任务执行
   └── 特点: 通过Client调用Server，包含AI决策逻辑

🏠 LLM Host (llm_search_host.py)
   ├── 功能: 接口适配器和业务门面
   ├── 职责: 保持原有API、协调Agent、管理生命周期
   └── 特点: 对外提供统一接口，内部使用MCP架构

连接流程: Host → Agent → Client → Server
数据流向: Server → Client → Agent → Host
    """)

async def main():
    """主测试函数"""
    print("开始测试修正后的MCP架构...")
    
    # 测试各个组件
    await test_mcp_client()
    await test_llm_agent()
    test_llm_search_host()
    await test_architecture_integration()
    
    # 打印架构总结
    print_architecture_summary()
    
    print("\n测试完成！")

if __name__ == "__main__":
    asyncio.run(main())
