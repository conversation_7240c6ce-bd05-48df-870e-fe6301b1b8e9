#!/usr/bin/env python3
"""
智能搜索流程测试脚本
测试完整的LLM对话 -> MCP工具调用流程
"""

import asyncio
import json
import logging
import os
import sys
import time
from pathlib import Path

# 添加项目路径
sys.path.append(str(Path(__file__).parent))

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def test_llm_conversation_agent():
    """测试LLM对话代理"""
    print("=" * 60)
    print("测试1: LLM对话代理测试")
    print("=" * 60)
    
    try:
        from src.search.llm_conversation_agent import LLMConversationAgent
        print("✅ 成功导入LLM对话代理")
        
        # 检查API密钥
        if not os.getenv("OPENAI_API_KEY") and not os.getenv("GOOGLE_API_KEY"):
            print("⚠️  警告: 未设置LLM API密钥，跳过对话代理测试")
            return False
        
        async def test_agent():
            agent = LLMConversationAgent()
            
            # 列出可用的搜索服务器
            servers = agent.list_available_servers()
            print(f"✅ 可用搜索服务器: {servers}")
            
            # 测试搜索任务
            task_description = """生成关于"人工智能伦理"的搜索查询，用于学术研究。
要求生成5-8个不同角度的查询，涵盖技术、社会、法律等方面。"""
            
            result = await agent.execute_search_task(task_description)
            
            if result["success"]:
                print("✅ 搜索任务执行成功")
                print(f"  LLM分析: {result['llm_analysis']}")
                print(f"  选择工具: {result['selected_tool']}")
                print(f"  工具结果: {json.dumps(result['tool_result'], ensure_ascii=False, indent=2)}")
            else:
                print(f"❌ 搜索任务失败: {result.get('error', 'Unknown error')}")
                return False
            
            return True
        
        # 运行异步测试
        return asyncio.run(test_agent())
        
    except Exception as e:
        print(f"❌ LLM对话代理测试失败: {e}")
        return False

def test_intelligent_llm_search():
    """测试智能LLM搜索接口"""
    print("\n" + "=" * 60)
    print("测试2: 智能LLM搜索接口测试")
    print("=" * 60)

    try:
        from src.search.intelligent_llm_search_host import LLM_search
        print("✅ 成功导入智能LLM搜索")
        
        # 检查API密钥
        if not os.getenv("OPENAI_API_KEY") and not os.getenv("GOOGLE_API_KEY"):
            print("⚠️  警告: 未设置LLM API密钥，跳过智能搜索测试")
            return False
        
        # 测试初始化（与原有接口相同）
        retriever = LLM_search(
            model='gemini-2.0-flash-thinking-exp-01-21',
            infer_type="OpenAI",
            engine='google',
            each_query_result=5
        )
        print("✅ 成功初始化智能LLM搜索实例")
        
        # 检查方法是否存在
        required_methods = ['get_queries', 'web_search', 'batch_web_search', 'snippet_filter']
        for method in required_methods:
            if hasattr(retriever, method):
                print(f"✅ 方法 {method} 存在")
            else:
                print(f"❌ 方法 {method} 不存在")
                return False
        
        # 测试查询生成
        start_time = time.time()
        queries = retriever.get_queries(
            topic="机器学习优化算法",
            description="研究机器学习中的优化技术和算法"
        )
        end_time = time.time()
        
        print(f"✅ 查询生成成功，耗时: {end_time - start_time:.2f}秒")
        print(f"  生成查询数量: {len(queries)}")
        print("  生成的查询:")
        for i, query in enumerate(queries[:3], 1):  # 只显示前3个
            print(f"    {i}. {query}")
        if len(queries) > 3:
            print(f"    ... 还有 {len(queries) - 3} 个查询")
        
        # 验证返回类型
        if isinstance(queries, list) and all(isinstance(q, str) for q in queries):
            print("✅ 返回类型正确 (List[str])")
        else:
            print("❌ 返回类型错误")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ 智能LLM搜索测试失败: {e}")
        return False

def test_original_interface_compatibility():
    """测试与原有接口的兼容性"""
    print("\n" + "=" * 60)
    print("测试3: 原有接口兼容性测试")
    print("=" * 60)

    try:
        from src.search.intelligent_llm_search_host import LLM_search
        
        # 模拟原有的使用方式（如start_pipeline.py中的使用）
        retriever = LLM_search(
            model='gemini-2.0-flash-thinking-exp-01-21', 
            infer_type="OpenAI", 
            engine='google', 
            each_query_result=10
        )
        
        print("✅ 原有初始化方式兼容")
        
        # 测试方法签名兼容性
        import inspect
        
        # 检查get_queries方法签名
        get_queries_sig = inspect.signature(retriever.get_queries)
        expected_params = ['topic', 'description']
        actual_params = list(get_queries_sig.parameters.keys())
        
        if all(param in actual_params for param in expected_params):
            print("✅ get_queries方法签名兼容")
        else:
            print(f"❌ get_queries方法签名不兼容: 期望{expected_params}, 实际{actual_params}")
            return False
        
        # 检查batch_web_search方法签名
        batch_search_sig = inspect.signature(retriever.batch_web_search)
        expected_params = ['queries', 'topic', 'top_n']
        actual_params = list(batch_search_sig.parameters.keys())
        
        if all(param in actual_params for param in expected_params):
            print("✅ batch_web_search方法签名兼容")
        else:
            print(f"❌ batch_web_search方法签名不兼容: 期望{expected_params}, 实际{actual_params}")
            return False
        
        print("✅ 所有方法签名与原有接口兼容")
        return True
        
    except Exception as e:
        print(f"❌ 原有接口兼容性测试失败: {e}")
        return False

def test_search_engine_extensibility():
    """测试搜索引擎扩展性"""
    print("\n" + "=" * 60)
    print("测试4: 搜索引擎扩展性测试")
    print("=" * 60)

    try:
        from src.search.intelligent_llm_search_host import LLM_search
        
        retriever = LLM_search()
        
        # 列出当前可用的搜索引擎
        engines = retriever.list_available_engines()
        print(f"✅ 当前可用搜索引擎: {engines}")
        
        # 测试添加新的搜索引擎
        new_engine_config = {
            "command": "python",
            "args": ["-m", "src.search.academic_search_mcp_server"],
            "env": {"PYTHONPATH": "."},
            "description": "学术搜索引擎，专门用于学术论文搜索"
        }
        
        retriever.add_search_engine("academic_search_mcp", new_engine_config)
        
        # 验证新引擎已添加
        updated_engines = retriever.list_available_engines()
        if "academic_search_mcp" in updated_engines:
            print("✅ 成功添加新的搜索引擎")
            print(f"  更新后的引擎列表: {updated_engines}")
        else:
            print("❌ 添加新搜索引擎失败")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ 搜索引擎扩展性测试失败: {e}")
        return False

def test_end_to_end_pipeline_simulation():
    """测试端到端流水线模拟"""
    print("\n" + "=" * 60)
    print("测试5: 端到端流水线模拟")
    print("=" * 60)
    
    try:
        # 模拟start_pipeline.py中的使用方式
        from src.search.intelligent_llm_search_host import LLM_search
        
        # 检查API密钥
        if not os.getenv("OPENAI_API_KEY") and not os.getenv("GOOGLE_API_KEY"):
            print("⚠️  警告: 未设置LLM API密钥，跳过端到端测试")
            return False
        
        print("🚀 模拟Pipeline调用...")
        
        # 模拟args参数
        class MockArgs:
            topic = "人工智能伦理"
            description = "研究人工智能发展中的伦理问题和挑战"
            top_n = 5
        
        args = MockArgs()
        
        # 步骤1: 创建retriever（原有方式）
        retriever = LLM_search(
            model='gemini-2.0-flash-thinking-exp-01-21', 
            infer_type="OpenAI", 
            engine='google', 
            each_query_result=10
        )
        print("✅ 步骤1: 创建LLM搜索实例")
        
        # 步骤2: 生成查询（原有方式）
        print("🔍 步骤2: 生成搜索查询...")
        queries = retriever.get_queries(topic=args.topic, description=args.description)
        print(f"✅ 生成 {len(queries)} 个查询")
        
        # 步骤3: 批量搜索（原有方式）
        print("🌐 步骤3: 执行批量搜索...")
        if os.getenv("SERP_API_KEY") or os.getenv("BING_SEARCH_V7_SUBSCRIPTION_KEY"):
            url_list = retriever.batch_web_search(
                queries=queries, 
                topic=args.topic, 
                top_n=int(args.top_n * 1.2)
            )
            print(f"✅ 获得 {len(url_list)} 个URL")
        else:
            print("⚠️  跳过批量搜索（无搜索API密钥）")
            url_list = ["http://example.com/1", "http://example.com/2"]
        
        # 步骤4: 验证结果格式
        print("📋 步骤4: 验证结果格式...")
        if isinstance(queries, list) and isinstance(url_list, list):
            print("✅ 结果格式正确，与原有接口完全兼容")
        else:
            print("❌ 结果格式错误")
            return False
        
        print("🎉 端到端流水线模拟成功！")
        print(f"  - 主题: {args.topic}")
        print(f"  - 查询数量: {len(queries)}")
        print(f"  - URL数量: {len(url_list)}")
        
        return True
        
    except Exception as e:
        print(f"❌ 端到端流水线模拟失败: {e}")
        return False

def test_enhanced_mcp_features():
    """测试增强的MCP功能"""
    print("\n" + "=" * 60)
    print("测试6: 增强的MCP功能测试")
    print("=" * 60)

    try:
        from src.search.intelligent_llm_search_host import LLM_search

        # 检查API密钥
        if not os.getenv("OPENAI_API_KEY") and not os.getenv("GOOGLE_API_KEY"):
            print("⚠️  警告: 未设置LLM API密钥，跳过增强功能测试")
            return False

        retriever = LLM_search()

        # 测试新增的crawl_urls方法
        if hasattr(retriever, 'crawl_urls'):
            print("✅ 发现新增的crawl_urls方法")

            # 测试URL爬取功能（使用示例URL）
            test_urls = [
                "https://example.com",
                "https://httpbin.org/html"
            ]

            try:
                # 注意：这个测试可能会因为网络或crawl4ai依赖而失败
                print("🕷️  测试URL爬取功能...")
                crawl_result = retriever.crawl_urls(test_urls, topic="测试主题")
                print(f"✅ URL爬取测试成功，结果类型: {type(crawl_result)}")
            except Exception as e:
                print(f"⚠️  URL爬取测试跳过（可能缺少crawl4ai依赖）: {e}")
        else:
            print("❌ 未发现crawl_urls方法")
            return False

        # 测试analyze_search_results功能（通过LLM对话）
        print("🔍 测试结果分析功能...")

        # 这里我们通过检查LLM对话代理的工具描述来验证功能
        available_engines = retriever.list_available_engines()
        print(f"✅ 可用搜索引擎: {available_engines}")

        # 验证LLM对话代理包含新的工具描述
        agent = retriever.conversation_agent
        if "analyze_search_results" in agent._get_system_prompt():
            print("✅ LLM对话代理包含结果分析功能")
        else:
            print("❌ LLM对话代理缺少结果分析功能")
            return False

        if "crawl_urls" in agent._get_system_prompt():
            print("✅ LLM对话代理包含URL爬取功能")
        else:
            print("❌ LLM对话代理缺少URL爬取功能")
            return False

        return True

    except Exception as e:
        print(f"❌ 增强MCP功能测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🚀 开始智能搜索流程测试")
    print(f"Python版本: {sys.version}")
    print(f"工作目录: {os.getcwd()}")
    
    # 检查环境变量
    print("\n📋 环境变量检查:")
    env_vars = ["OPENAI_API_KEY", "GOOGLE_API_KEY", "SERP_API_KEY", "BING_SEARCH_V7_SUBSCRIPTION_KEY"]
    for var in env_vars:
        value = os.getenv(var)
        status = "✅ 已设置" if value else "❌ 未设置"
        print(f"  {var}: {status}")
    
    # 运行测试
    tests = [
        ("LLM对话代理", test_llm_conversation_agent),
        ("智能LLM搜索接口", test_intelligent_llm_search),
        ("原有接口兼容性", test_original_interface_compatibility),
        ("搜索引擎扩展性", test_search_engine_extensibility),
        ("端到端流水线模拟", test_end_to_end_pipeline_simulation),
        ("增强MCP功能", test_enhanced_mcp_features)
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ 测试 '{test_name}' 发生异常: {e}")
            results.append((test_name, False))
    
    # 总结测试结果
    print("\n" + "=" * 60)
    print("📊 测试结果总结")
    print("=" * 60)
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"  {test_name}: {status}")
    
    print(f"\n总计: {passed}/{total} 个测试通过")
    
    if passed == total:
        print("🎉 所有测试通过！智能搜索流程实现成功。")
        print("\n📝 实现特性:")
        print("✅ LLM智能选择搜索工具")
        print("✅ 完全兼容原有LLM_search接口")
        print("✅ 支持动态添加新的搜索引擎")
        print("✅ 通过MCP协议实现模块化架构")
        print("✅ 端到端流程与原有Pipeline完全兼容")
    else:
        print("⚠️  部分测试失败，请检查实现和配置。")

if __name__ == "__main__":
    main()
