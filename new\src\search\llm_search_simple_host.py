#!/usr/bin/env python3
"""
简化版LLM搜索主机 - 直接使用MCP Client
适用于不需要复杂AI决策的场景

架构对比：
1. 复杂版本：Host → Agent → Client → Server (包含LLM智能决策)
2. 简化版本：Host → Client → Server (直接调用，无AI决策)

使用场景：
- 固定的工具调用模式
- 不需要复杂的AI决策逻辑
- 追求简单和高效
- 明确知道要调用哪个工具
"""

import asyncio
import logging
import os
import traceback
from typing import List, Literal, Optional

from .llm_search_mcp_client import MCPClient, create_mcp_client_from_config

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class SimpleLLMSearch:
    """
    简化版LLM搜索类 - 直接使用MCP Client
    
    优势：
    - 简单直接，无复杂的AI决策开销
    - 连接管理简单
    - 调用路径短，性能更好
    
    劣势：
    - 无法智能选择工具和策略
    - 无法处理复杂的多步骤任务
    - 扩展性相对较差
    """
    
    def __init__(
        self,
        model: str = "gemini-2.0-flash-thinking-exp-01-21",
        infer_type: str = "OpenAI",
        engine: Literal["google", "baidu", "bing"] = "google",
        each_query_result: int = 10,
        filter_date: Optional[str] = None,
        max_workers: int = 10,
    ):
        """
        初始化简化版LLM搜索
        
        Args:
            model: LLM模型名称
            infer_type: 推理类型 (OpenAI, Google, local)
            engine: 搜索引擎 (google, baidu, bing)
            each_query_result: 每个查询的结果数量
            filter_date: 日期过滤器
            max_workers: 最大并发工作数
        """
        self.model = model
        self.infer_type = infer_type
        self.engine = engine
        self.each_query_result = each_query_result
        self.filter_date = filter_date
        self.max_workers = max_workers
        
        self._mcp_client: Optional[MCPClient] = None
        
        self._check_api_keys()
        
        logger.info(f"Simple LLM Search initialized with model={model}, engine={engine}")
    
    def _check_api_keys(self):

        llm_keys = ["OPENAI_API_KEY", "GOOGLE_API_KEY"]
        llm_available = any(os.getenv(key) for key in llm_keys)

        search_keys = ["SERP_API_KEY", "BING_SEARCH_V7_SUBSCRIPTION_KEY"]
        search_available = any(os.getenv(key) for key in search_keys)
        
        if not llm_available:
            logger.warning("No LLM API key found. Query generation may fail.")
        
        if not search_available:
            raise ValueError(
                "No valid search engine key provided. Please set SERP_API_KEY or BING_SEARCH_V7_SUBSCRIPTION_KEY."
            )
    
    async def _get_mcp_client(self) -> MCPClient:
        if self._mcp_client is None or not self._mcp_client.is_connected:
            self._mcp_client = await create_mcp_client_from_config()
        return self._mcp_client
    
    async def _cleanup_client(self):
        if self._mcp_client and self._mcp_client.is_connected:
            await self._mcp_client.disconnect()
            self._mcp_client = None
    
    def get_queries(self, topic: str, description: str = "") -> List[str]:
        return asyncio.run(self._async_get_queries(topic, description))
    
    async def _async_get_queries(self, topic: str, description: str = "") -> List[str]:
        try:
            client = await self._get_mcp_client()
            
            # 直接调用MCP工具 - 无AI决策，直接使用generate_search_queries
            result = await client.call_tool(
                "generate_search_queries",
                {
                    "topic": topic,
                    "description": description,
                    "model": self.model
                }
            )
            
            queries = result["queries"]
            logger.info(f"Generated {len(queries)} queries: {queries}")
            return queries
            
        except Exception as e:
            logger.error(f"Error generating queries: {e}")
            logger.error(traceback.format_exc())
            raise
        finally:
            await self._cleanup_client()
    
    def web_search(self, query: str) -> dict:
        return asyncio.run(self._async_web_search(query))
    
    async def _async_web_search(self, query: str):
        try:
            client = await self._get_mcp_client()
         
            result = await client.call_tool(
                "web_search",
                {
                    "queries": [query],
                    "topic": query,
                    "top_n": self.each_query_result,
                    "engine": self.engine
                }
            )
            
            urls = result["urls"]
            web_snippets = {}
            for idx, url in enumerate(urls):
                web_snippets[idx] = {
                    "title": f"Result {idx + 1}",
                    "url": url,
                    "snippet": f"Search result for: {query}",
                }
            
            return web_snippets
            
        except Exception as e:
            logger.error(f"Error in web search for query '{query}': {e}")
            raise
        finally:
            await self._cleanup_client()
    
    def batch_web_search(self, queries: List[str], topic: str, top_n: int = 20) -> List[str]:
        return asyncio.run(self._async_batch_web_search(queries, topic, top_n))
    
    async def _async_batch_web_search(self, queries: List[str], topic: str, top_n: int = 20) -> List[str]:
        try:
            client = await self._get_mcp_client()
            result = await client.call_tool(
                "web_search",
                {
                    "queries": queries,
                    "topic": topic,
                    "top_n": top_n,
                    "engine": self.engine
                }
            )
            
            urls = result["urls"]
            logger.info(f"Returning top {len(urls)} most relevant URLs.")
            return urls
            
        except Exception as e:
            logger.error(f"Error in batch web search: {e}")
            logger.error(traceback.format_exc())
            raise
        finally:
            await self._cleanup_client()
    
    def snippet_filter(self, topic: str, snippet: str) -> float:
        """
        计算主题和片段之间的相似度分数
        
        Args:
            topic: 搜索主题
            snippet: 要比较的文本片段
            
        Returns:
            float: 0到100之间的相似度分数
        """
        try:
            # 简化实现，可以后续通过MCP工具增强
            logger.info(f"Calculating similarity for topic: {topic}")
            return 75.0  # 默认分数
            
        except Exception as e:
            logger.error(f"Error calculating similarity score: {e}")
            return 0.0
    
    def crawl_urls(self, urls: List[str], topic: str = "") -> dict:
        return asyncio.run(self._async_crawl_urls(urls, topic))
    
    async def _async_crawl_urls(self, urls: List[str], topic: str = "") -> dict:
        """异步爬取URLs"""
        try:
            client = await self._get_mcp_client()
            
            # 直接调用MCP工具 - 无AI决策，直接使用crawl_urls
            result = await client.call_tool(
                "crawl_urls",
                {
                    "topic": topic,
                    "url_list": urls
                }
            )
            
            return result
            
        except Exception as e:
            logger.error(f"Error in URL crawling: {e}")
            raise
        finally:
            await self._cleanup_client()
    
    def add_search_engine(self, name: str, config: dict):
        """
        添加新的搜索引擎（简化版暂不支持）
        
        Args:
            name: 搜索引擎名称
            config: 搜索引擎配置
        """
        logger.warning("Adding search engines not supported in simplified version")
    
    def list_available_engines(self) -> List[str]:
        """列出可用的搜索引擎"""
        return ["google", "bing", "baidu"]

# 为了保持向后兼容性，提供一个工厂函数
def create_simple_llm_search(
    model: str = "gemini-2.0-flash-thinking-exp-01-21",
    infer_type: str = "OpenAI",
    engine: Literal["google", "baidu", "bing"] = "google",
    each_query_result: int = 10,
    filter_date: Optional[str] = None,
    max_workers: int = 10,
) -> SimpleLLMSearch:
    """
    创建简化版LLM搜索实例的工厂函数
    
    Args:
        model: LLM模型名称
        infer_type: 推理类型
        engine: 搜索引擎
        each_query_result: 每个查询的结果数量
        filter_date: 日期过滤器
        max_workers: 最大并发工作数
        
    Returns:
        SimpleLLMSearch: 简化版LLM搜索实例
    """
    return SimpleLLMSearch(
        model=model,
        infer_type=infer_type,
        engine=engine,
        each_query_result=each_query_result,
        filter_date=filter_date,
        max_workers=max_workers
    )
