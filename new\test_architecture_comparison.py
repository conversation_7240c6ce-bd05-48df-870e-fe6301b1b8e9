#!/usr/bin/env python3
"""
MCP架构对比测试
比较两种架构的优缺点：
1. 复杂版本：Host → Agent → Client → Server (包含LLM智能决策)
2. 简化版本：Host → Client → Server (直接调用，无AI决策)
"""

import asyncio
import time
import logging
import sys
import os

# 添加项目路径
sys.path.append(os.path.join(os.path.dirname(__file__), '.'))

from src.search.llm_search_host import LLM_search as ComplexLLMSearch
from src.search.llm_search_simple_host import SimpleLLMSearch

logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def test_complex_version():
    """测试复杂版本：Host → Agent → Client → Server"""
    print("\n=== 测试复杂版本 (Host → Agent → Client → Server) ===")
    
    try:
        start_time = time.time()
        
        # 创建复杂版本实例
        llm_search = ComplexLLMSearch(
            model="gemini-2.0-flash-thinking-exp-01-21",
            engine="google",
            each_query_result=5
        )
        
        # 测试查询生成
        queries = llm_search.get_queries(
            topic="机器学习优化",
            description="研究机器学习中的优化算法"
        )
        
        end_time = time.time()
        
        print(f"✓ 复杂版本测试成功")
        print(f"  生成查询数: {len(queries)}")
        print(f"  查询示例: {queries[:2]}")
        print(f"  执行时间: {end_time - start_time:.2f}秒")
        print(f"  架构路径: Host → Agent → Client → Server")
        print(f"  特点: 包含LLM智能决策，可以选择最合适的工具和策略")
        
        return True, end_time - start_time, len(queries)
        
    except Exception as e:
        print(f"✗ 复杂版本测试失败: {e}")
        return False, 0, 0

def test_simple_version():
    """测试简化版本：Host → Client → Server"""
    print("\n=== 测试简化版本 (Host → Client → Server) ===")
    
    try:
        start_time = time.time()
        
        # 创建简化版本实例
        llm_search = SimpleLLMSearch(
            model="gemini-2.0-flash-thinking-exp-01-21",
            engine="google",
            each_query_result=5
        )
        
        # 测试查询生成
        queries = llm_search.get_queries(
            topic="机器学习优化",
            description="研究机器学习中的优化算法"
        )
        
        end_time = time.time()
        
        print(f"✓ 简化版本测试成功")
        print(f"  生成查询数: {len(queries)}")
        print(f"  查询示例: {queries[:2]}")
        print(f"  执行时间: {end_time - start_time:.2f}秒")
        print(f"  架构路径: Host → Client → Server")
        print(f"  特点: 直接调用MCP工具，无AI决策开销")
        
        return True, end_time - start_time, len(queries)
        
    except Exception as e:
        print(f"✗ 简化版本测试失败: {e}")
        return False, 0, 0

def print_architecture_comparison():
    """打印架构对比分析"""
    print("\n" + "="*80)
    print("MCP架构对比分析")
    print("="*80)
    
    print("""
🔄 复杂版本：Host → Agent → Client → Server
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   LLM Host      │    │   LLM Agent     │    │   MCP Client    │    │   MCP Server    │
│   (接口适配)    │───►│   (智能协调)    │───►│   (通信桥梁)    │───►│   (业务实现)    │
│                 │    │                 │    │                 │    │                 │
│ • 保持原有API   │    │ • LLM分析任务   │    │ • 连接管理      │    │ • 工具实现      │
│ • 异步转换      │    │ • 选择工具      │    │ • 消息传递      │    │ • LLM调用       │
│ • 资源清理      │    │ • 智能决策      │    │ • 协议处理      │    │ • 搜索执行      │
└─────────────────┘    └─────────────────┘    └─────────────────┘    └─────────────────┘

优势：
✓ 智能工具选择 - LLM分析任务并选择最合适的工具
✓ 复杂任务处理 - 可以组合多个工具完成复杂任务
✓ 自适应策略 - 根据不同场景调整搜索策略
✓ 高扩展性 - 易于添加新的搜索引擎和工具
✓ 错误恢复 - 智能处理错误和重试

劣势：
✗ 性能开销 - LLM调用增加延迟
✗ 复杂性高 - 更多的组件和交互
✗ 资源消耗 - 需要更多的内存和计算资源
✗ 调试困难 - 多层架构增加调试复杂度

⚡ 简化版本：Host → Client → Server
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Simple Host   │    │   MCP Client    │    │   MCP Server    │
│   (直接调用)    │───►│   (通信桥梁)    │───►│   (业务实现)    │
│                 │    │                 │    │                 │
│ • 保持原有API   │    │ • 连接管理      │    │ • 工具实现      │
│ • 直接工具调用  │    │ • 消息传递      │    │ • LLM调用       │
│ • 简单高效      │    │ • 协议处理      │    │ • 搜索执行      │
└─────────────────┘    └─────────────────┘    └─────────────────┘

优势：
✓ 高性能 - 无AI决策开销，调用路径短
✓ 简单直接 - 架构简单，易于理解和维护
✓ 资源节省 - 内存和计算资源消耗少
✓ 调试容易 - 调用路径清晰，问题定位简单
✓ 稳定可靠 - 组件少，故障点少

劣势：
✗ 固定模式 - 无法智能选择工具和策略
✗ 扩展性差 - 添加新功能需要修改代码
✗ 适应性弱 - 无法根据场景调整策略
✗ 错误处理 - 简单的错误处理机制

📊 使用建议：

复杂版本适用于：
• 需要智能决策的场景
• 多种搜索引擎和工具的选择
• 复杂的多步骤搜索任务
• 研究和实验性项目
• 对性能要求不是特别高的场景

简化版本适用于：
• 固定的搜索模式
• 对性能要求高的场景
• 生产环境的稳定服务
• 资源受限的环境
• 明确知道要使用哪个工具的场景
    """)

def print_decision_guide():
    """打印决策指南"""
    print("\n" + "="*60)
    print("架构选择决策指南")
    print("="*60)
    print("""
🤔 如何选择合适的架构？

问题1: 你需要AI智能选择搜索策略吗？
├─ 是 → 选择复杂版本
└─ 否 → 继续下一个问题

问题2: 你的搜索模式是固定的吗？
├─ 是 → 选择简化版本
└─ 否 → 选择复杂版本

问题3: 对性能有严格要求吗？
├─ 是 → 选择简化版本
└─ 否 → 选择复杂版本

问题4: 需要处理复杂的多步骤任务吗？
├─ 是 → 选择复杂版本
└─ 否 → 选择简化版本

问题5: 团队对复杂架构的维护能力如何？
├─ 强 → 可以选择复杂版本
└─ 弱 → 建议选择简化版本

💡 总结：
• 如果你明确知道要调用哪个工具，选择简化版本
• 如果你需要AI帮你决策，选择复杂版本
• 如果追求性能和稳定性，选择简化版本
• 如果追求智能化和扩展性，选择复杂版本
    """)

def main():
    """主测试函数"""
    print("开始MCP架构对比测试...")
    
    # 测试两种版本
    complex_success, complex_time, complex_queries = test_complex_version()
    simple_success, simple_time, simple_queries = test_simple_version()
    
    # 打印对比结果
    print("\n" + "="*60)
    print("测试结果对比")
    print("="*60)
    
    if complex_success and simple_success:
        print(f"复杂版本: ✓ 成功 | 时间: {complex_time:.2f}s | 查询数: {complex_queries}")
        print(f"简化版本: ✓ 成功 | 时间: {simple_time:.2f}s | 查询数: {simple_queries}")
        
        if simple_time < complex_time:
            speedup = complex_time / simple_time
            print(f"\n⚡ 简化版本比复杂版本快 {speedup:.1f}x")
        else:
            print(f"\n🤔 复杂版本在这次测试中表现更好")
    else:
        print(f"复杂版本: {'✓' if complex_success else '✗'}")
        print(f"简化版本: {'✓' if simple_success else '✗'}")
    
    # 打印架构对比分析
    print_architecture_comparison()
    
    # 打印决策指南
    print_decision_guide()
    
    print("\n测试完成！")

if __name__ == "__main__":
    main()
