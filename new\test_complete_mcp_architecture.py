#!/usr/bin/env python3
"""
完整的MCP架构测试
验证Host、Client、Server的正确分工和配置独立性
"""

import asyncio
import json
import logging
import os
import sys
import time

# 添加项目路径
sys.path.append(os.path.join(os.path.dirname(__file__), '.'))

from src.search.llm_search_mcp_client import MCPClient, create_mcp_client_from_config

logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

async def test_server_config_independence():
    """测试Server配置独立性 - 核心测试"""
    print("\n=== 测试Server配置独立性（核心测试）===")
    
    client = None
    try:
        # 1. 创建客户端
        client = await create_mcp_client_from_config()
        print(f"✓ 客户端连接状态: {client.is_connected}")
        
        # 2. 测试不传递任何配置参数
        print("\n测试1: 不传递任何配置参数...")
        result1 = await client.call_tool(
            "generate_search_queries",
            {
                "topic": "人工智能",
                "description": "研究人工智能的发展历程"
                # 注意：不传递model参数，Server应该使用配置文件中的默认值
            }
        )
        
        print(f"✓ Server使用默认配置成功")
        print(f"  生成查询数: {len(result1['queries'])}")
        print(f"  使用的模型: {result1['model']}")
        print(f"  查询示例: {result1['queries'][:2]}")
        
        # 3. 测试web_search不传递配置
        print("\n测试2: web_search不传递配置参数...")
        result2 = await client.call_tool(
            "web_search",
            {
                "queries": ["人工智能发展", "AI技术进展"],
                "topic": "人工智能"
                # 注意：不传递engine和top_n参数，Server应该使用默认值
            }
        )
        
        print(f"✓ Server使用默认配置成功")
        print(f"  找到URL数: {len(result2['urls'])}")
        print(f"  使用的引擎: {result2['engine']}")
        print(f"  返回数量: {result2['top_n']}")
        
        # 4. 测试crawl_urls不传递配置
        print("\n测试3: crawl_urls不传递配置参数...")
        test_urls = [
            "https://example.com/ai1",
            "https://example.com/ai2"
        ]
        result3 = await client.call_tool(
            "crawl_urls",
            {
                "topic": "人工智能",
                "url_list": test_urls
                # 注意：不传递任何配置参数，Server应该使用默认值
            }
        )
        
        print(f"✓ Server使用默认配置成功")
        print(f"  处理URL数: {result3['total_urls']}")
        
        return True
        
    except Exception as e:
        print(f"✗ Server配置独立性测试失败: {e}")
        logger.error(f"Server config test error: {e}")
        return False
    finally:
        if client:
            await client.disconnect()

def test_host_interface_compatibility():
    """测试Host接口兼容性"""
    print("\n=== 测试Host接口兼容性 ===")
    
    try:
        # 测试导入
        from src.search.llm_search_correct_host import LLM_search
        
        # 创建Host实例（传入的配置仅用于兼容性）
        llm_search = LLM_search(
            model="gemini-2.0-flash-thinking-exp-01-21",
            infer_type="OpenAI"
        )
        print("✓ Host实例创建成功")
        
        # 测试所有原有接口方法存在
        required_methods = [
            'get_queries', 'web_search', 'batch_web_search', 
            'snippet_filter', 'crawl_urls', 'add_search_engine', 
            'list_available_engines'
        ]
        
        print("✓ 接口兼容性检查:")
        all_exist = True
        for method in required_methods:
            exists = hasattr(llm_search, method)
            print(f"  - {method}(): {'✓' if exists else '✗'}")
            if not exists:
                all_exist = False
        
        return all_exist
        
    except Exception as e:
        print(f"✗ Host接口兼容性测试失败: {e}")
        return False

async def test_host_llm_functionality():
    """测试Host的LLM功能"""
    print("\n=== 测试Host的LLM功能 ===")
    
    try:
        from src.search.llm_search_correct_host import LLM_search
        
        # 创建Host实例
        llm_search = LLM_search()
        print("✓ Host实例创建成功")
        
        # 测试LLM工具选择功能
        print("\n测试LLM工具选择...")
        start_time = time.time()
        
        # 模拟Host内部的LLM调用流程
        client = await llm_search._get_mcp_client()
        available_tools = await llm_search._get_available_tools()
        
        print(f"✓ Host成功询问到 {len(available_tools)} 个可用工具:")
        for tool in available_tools:
            print(f"  - {tool['name']}: {tool['description'][:50]}...")
        
        # 测试LLM工具选择和调用
        result = await llm_search._llm_select_and_call_tool(
            "为人工智能主题生成搜索查询",
            topic="人工智能",
            description="研究人工智能的基本概念和应用"
        )
        
        end_time = time.time()
        
        print(f"✓ Host LLM工具选择和调用成功")
        print(f"  执行时间: {end_time - start_time:.2f}秒")
        print(f"  生成查询数: {len(result.get('queries', []))}")
        
        return True
        
    except Exception as e:
        print(f"✗ Host LLM功能测试失败: {e}")
        logger.error(f"Host LLM test error: {e}")
        return False

async def test_end_to_end_workflow():
    """测试端到端工作流程"""
    print("\n=== 测试端到端工作流程 ===")
    
    try:
        from src.search.llm_search_correct_host import LLM_search
        
        # 创建Host实例
        llm_search = LLM_search()
        print("✓ 创建Host实例")
        
        # 测试完整的搜索流程
        print("\n执行完整搜索流程...")
        start_time = time.time()
        
        # 1. 生成查询
        queries = llm_search.get_queries(
            topic="量子计算",
            description="研究量子计算的基本原理和应用前景"
        )
        print(f"✓ 步骤1: 生成了 {len(queries)} 个查询")
        
        # 2. 批量搜索（使用前2个查询）
        urls = llm_search.batch_web_search(
            queries=queries[:2],
            topic="量子计算",
            top_n=5
        )
        print(f"✓ 步骤2: 找到了 {len(urls)} 个相关URL")
        
        end_time = time.time()
        print(f"✓ 端到端工作流程完成，总耗时: {end_time - start_time:.2f}秒")
        
        return True
        
    except Exception as e:
        print(f"✗ 端到端工作流程测试失败: {e}")
        logger.error(f"End-to-end test error: {e}")
        return False

def print_architecture_summary():
    """打印架构总结"""
    print("\n" + "="*80)
    print("MCP架构实现总结")
    print("="*80)
    print("""
🎯 用户要求实现情况:

✅ Host不拥有工具信息
   - Host通过Client动态询问Server获取工具信息
   - Host不预设任何工具列表或配置

✅ Host仅为LLM调用器
   - Host只包含LLM调用逻辑和提示词生成
   - Host使用LLM分析任务并选择合适的工具
   - Host不包含任何具体的业务逻辑实现

✅ 配置在Server中实现
   - Server从配置文件读取所有默认配置
   - Server不依赖Host传递配置参数
   - 所有搜索设置都在Server中管理

✅ 接口与原始LLM_search对齐
   - 保持完全相同的API接口
   - 用户无需修改任何调用代码
   - 内部架构重构但外部接口不变

🏗️ 架构组件职责:

📱 Host (LLM调用器)
├── 向Client询问Server工具信息
├── 使用LLM分析任务并选择工具
├── 通过Client调用Server工具
└── 保持原有API接口兼容性

🔌 Client (通信桥梁)
├── 管理与Server的连接
├── 传递工具信息查询请求
├── 执行工具调用请求
└── 返回Server响应结果

🖥️ Server (工具+配置实现)
├── 从配置文件加载所有设置
├── 实现具体的搜索工具逻辑
├── 处理LLM调用和内容处理
└── 管理所有业务逻辑和资源

🔄 正确的调用流程:
1. Host向Client询问Server可用工具
2. Host使用LLM分析任务并选择工具
3. Host通过Client调用Server工具（仅传递用户参数）
4. Server使用配置文件中的默认值执行工具
5. Host处理结果并返回给调用方
    """)

async def main():
    """主测试函数"""
    print("开始完整的MCP架构测试...")
    print("验证Host、Client、Server的正确分工和配置独立性")
    
    # 执行所有测试
    test_results = {}
    
    print("\n" + "="*60)
    print("执行测试套件")
    print("="*60)
    
    # 测试1: Server配置独立性（最重要）
    test_results['server_config'] = await test_server_config_independence()
    
    # 测试2: Host接口兼容性
    test_results['host_interface'] = test_host_interface_compatibility()
    
    # 测试3: Host LLM功能
    test_results['host_llm'] = await test_host_llm_functionality()
    
    # 测试4: 端到端工作流程
    test_results['end_to_end'] = await test_end_to_end_workflow()
    
    # 打印架构总结
    print_architecture_summary()
    
    # 总结测试结果
    print("\n" + "="*80)
    print("测试结果总结")
    print("="*80)
    
    all_passed = all(test_results.values())
    
    if all_passed:
        print("🎉 所有测试通过！MCP架构实现完全正确")
        print("\n✅ 测试结果:")
        print(f"  Server配置独立性: {'✓' if test_results['server_config'] else '✗'}")
        print(f"  Host接口兼容性: {'✓' if test_results['host_interface'] else '✗'}")
        print(f"  Host LLM功能: {'✓' if test_results['host_llm'] else '✗'}")
        print(f"  端到端工作流程: {'✓' if test_results['end_to_end'] else '✗'}")
        
        print("\n🎯 架构完全符合用户要求:")
        print("• Host仅作为LLM调用器，不拥有工具信息")
        print("• Server从配置文件读取所有配置")
        print("• Host通过Client动态询问Server工具信息")
        print("• 保持与原有LLM_search接口完全兼容")
        
    else:
        print("❌ 部分测试失败，需要进一步检查:")
        print(f"  Server配置独立性: {'✓' if test_results['server_config'] else '✗'}")
        print(f"  Host接口兼容性: {'✓' if test_results['host_interface'] else '✗'}")
        print(f"  Host LLM功能: {'✓' if test_results['host_llm'] else '✗'}")
        print(f"  端到端工作流程: {'✓' if test_results['end_to_end'] else '✗'}")
    
    print("\n测试完成！")
    return all_passed

if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
