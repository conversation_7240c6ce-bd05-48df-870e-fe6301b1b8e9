import asyncio
import json
import logging
import os
from typing import Dict, List, Any, Optional

from mcp import ClientSession
from mcp.client.stdio import stdio_client
from mcp.types import CallToolRequest, TextContent

try:
    from ..request.wrapper import RequestWrapper
except ImportError:
    import sys
    sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..'))
    from request.wrapper import RequestWrapper

logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class LLMConversationAgent:
    
    def __init__(
        self,
        model: str = "gemini-2.0-flash-thinking-exp-01-21",
        infer_type: str = "OpenAI"
    ):
        self.model = model
        self.infer_type = infer_type
        self.request_wrapper = RequestWrapper(model=model, infer_type=infer_type)
        
        self.search_servers = {
            "llm_search_mcp": {
                "command": "python",
                "args": ["-m", "src.search.llm_search_mcp_server"],
                "env": {
                    "PYTHONPATH": ".",
                    "OPENAI_API_KEY": os.getenv("OPENAI_API_KEY", "sk-qRnt09M8Ncvl0Dwk1aA374833a6a4dB5B6A6De2e1901D146"),
                    "GOOGLE_API_KEY": os.getenv("GOOGLE_API_KEY", "sk-qRnt09M8Ncvl0Dwk1aA374833a6a4dB5B6A6De2e1901D146"),
                    "SERP_API_KEY": os.getenv("SERP_API_KEY", "2a69d2cf83fff08dfc77b82469587c87a0a4bb6c99954c37a0d005da19f060e1"),
                    # "BING_SEARCH_V7_SUBSCRIPTION_KEY": os.getenv("BING_SEARCH_V7_SUBSCRIPTION_KEY", "")
                    # "duckduckgo_key": os.getenv("DUCKDUCKGO_KEY", "")
                },
                "description": "LLM搜索功能，支持查询生成、网络搜索、结果分析和网页爬取",
                "tools": [
                    "generate_search_queries - 基于LLM生成优化的搜索查询",
                    "web_search - 使用提供的查询执行网络搜索",
                    "analyze_search_results - 分析和过滤搜索结果的相关性",
                    "crawl_urls - 异步爬取URL列表并处理内容"
                ]
            }
        }
        
        logger.info(f"LLM Conversation Agent initialized with model={model}")
    
    def _get_system_prompt(self) -> str:
        available_tools = []
        for server_name, config in self.search_servers.items():
            tools_list = "\n    ".join(config.get('tools', []))
            available_tools.append(f"- {server_name}: {config['description']}\n    工具: {tools_list}")

        tools_description = "\n".join(available_tools)

        return f"""你是一个智能搜索协调器。用户会给你一个搜索任务，你需要：

1. 分析用户的搜索需求
2. 选择最合适的搜索工具
3. 调用相应的MCP工具执行搜索
4. 返回搜索结果

可用的搜索服务器：
{tools_description}

你可以调用以下MCP工具：
- generate_search_queries: 基于LLM生成优化的搜索查询
- web_search: 使用提供的查询执行网络搜索
- analyze_search_results: 分析和过滤搜索结果的相关性
- crawl_urls: 异步爬取URL列表并处理内容

请根据用户需求选择最合适的工具和参数。对于复杂任务，可以组合使用多个工具。"""

    async def _connect_to_mcp_server(self, server_name: str) -> tuple:
        if server_name not in self.search_servers:
            raise ValueError(f"Unknown search server: {server_name}")
        
        config = self.search_servers[server_name]
        
        read_stream, write_stream = await stdio_client(
            command=config["command"],
            args=config.get("args", []),
            env=config.get("env", None)
        ).__aenter__()
        
        session = await ClientSession(read_stream, write_stream).__aenter__()
        await session.initialize()
        
        logger.info(f"Connected to MCP server: {server_name}")
        return session, read_stream, write_stream
    
    async def _disconnect_from_mcp_server(self, session, read_stream, write_stream, server_config):
        try:
            if session:
                await session.__aexit__(None, None, None)
            if read_stream and write_stream:
                await stdio_client(
                    command=server_config["command"],
                    args=server_config.get("args", []),
                    env=server_config.get("env", None)
                ).__aexit__(None, None, None)
        except Exception as e:
            logger.error(f"Error disconnecting from MCP server: {e}")
    
    async def _call_mcp_tool(self, server_name: str, tool_name: str, arguments: Dict[str, Any]) -> Dict[str, Any]:
 
        session = None
        read_stream = None
        write_stream = None
        
        try:

            session, read_stream, write_stream = await self._connect_to_mcp_server(server_name)

            result = await session.call_tool(
                CallToolRequest(
                    name=tool_name,
                    arguments=arguments
                )
            )
            
            if result.content and len(result.content) > 0:
                content = result.content[0]
                if isinstance(content, TextContent):
                    return json.loads(content.text)
            
            raise ValueError(f"No valid response received from tool {tool_name}")
            
        finally:
            if session and read_stream and write_stream:
                await self._disconnect_from_mcp_server(
                    session, read_stream, write_stream, 
                    self.search_servers[server_name]
                )
    
    def _create_conversation_messages(self, task_description: str) -> List[Dict[str, str]]:
        # TODO：所有的llm交互json格式全部使用schema规范
        return [
            {"role": "system", "content": self._get_system_prompt()},
            {"role": "user", "content": f"""请帮我执行以下搜索任务：

任务描述：{task_description}

请分析这个任务，选择合适的搜索工具，并执行搜索。

请按以下格式返回结果：
```json
{{
    "analysis": "任务分析",
    "selected_tool": "选择的工具名称",
    "tool_call": {{
        "server": "MCP服务器名称",
        "tool": "工具名称", 
        "arguments": {{...}}
    }},
    "execute": true
}}
```"""}
        ]
    
    async def execute_search_task(self, task_description: str) -> Dict[str, Any]:

        try:
            logger.info(f"Executing search task: {task_description}")
            
            messages = self._create_conversation_messages(task_description)
            
            response = await self.request_wrapper.async_request(
                messages=messages,
                temperature=0.1,
                max_tokens=2000
            )
            
            logger.info(f"LLM response: {response}")
            
            try:
                response_text = response.strip()
                if "```json" in response_text:
                    json_start = response_text.find("```json") + 7
                    json_end = response_text.find("```", json_start)
                    json_text = response_text[json_start:json_end].strip()
                else:
                    json_text = response_text
                
                llm_decision = json.loads(json_text)
                
            except json.JSONDecodeError as e:
                logger.error(f"Failed to parse LLM response as JSON: {e}")
                llm_decision = {
                    "analysis": "JSON解析失败，使用默认搜索",
                    "selected_tool": "llm_search_mcp",
                    "tool_call": {
                        "server": "llm_search_mcp",
                        "tool": "full_search_pipeline",
                        "arguments": {"topic": task_description}
                    },
                    "execute": True
                }
            
            if llm_decision.get("execute", False):
                tool_call = llm_decision["tool_call"]
                
                logger.info(f"Executing tool: {tool_call}")
                
                result = await self._call_mcp_tool(
                    server_name=tool_call["server"],
                    tool_name=tool_call["tool"],
                    arguments=tool_call["arguments"]
                )
                
                return {
                    "task_description": task_description,
                    "llm_analysis": llm_decision.get("analysis", ""),
                    "selected_tool": llm_decision.get("selected_tool", ""),
                    "tool_result": result,
                    "success": True
                }
            else:
                return {
                    "task_description": task_description,
                    "llm_analysis": llm_decision.get("analysis", ""),
                    "error": "LLM decided not to execute the task",
                    "success": False
                }
                
        except Exception as e:
            logger.error(f"Error executing search task: {e}")
            return {
                "task_description": task_description,
                "error": str(e),
                "success": False
            }
    
    def add_search_server(self, name: str, config: Dict[str, Any]):
        self.search_servers[name] = config
        logger.info(f"Added new search server: {name}")
    
    def list_available_servers(self) -> List[str]:
        return list(self.search_servers.keys())

async def execute_intelligent_search(
    task_description: str,
    model: str = "gemini-2.0-flash-thinking-exp-01-21",
    infer_type: str = "OpenAI"
) -> Dict[str, Any]:

    agent = LLMConversationAgent(model=model, infer_type=infer_type)
    return await agent.execute_search_task(task_description)
