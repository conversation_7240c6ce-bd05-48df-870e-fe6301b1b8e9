#!/usr/bin/env python3
"""
智能LLM搜索主机 - 结合LLM对话代理和更新的MCP服务器
替代原有的LLM_search模块，通过LLM对话选择合适的搜索工具
保持与原有接口完全一致，实现无缝替换

工作流程：
1. Pipeline调用LLM_search方法
2. 启动LLM对话程序  
3. LLM分析任务并选择合适的MCP搜索工具
4. 执行搜索并返回结果
"""

import asyncio
import logging
import os
import traceback
from typing import List, Literal, Optional

from .llm_search_agent import LLMConversationAgent

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class LLM_search:
    """
    智能LLM搜索类 - 通过LLM对话选择最佳搜索策略
    
    保持与原有LLM_search类完全相同的接口，但内部通过LLM对话
    来智能选择合适的搜索工具和策略
    """
    
    def __init__(
        self,
        model: str = "gemini-2.0-flash-thinking-exp-01-21",
        infer_type: str = "OpenAI",
        engine: Literal["google", "baidu", "bing"] = "google",
        each_query_result: int = 10,
        filter_date: Optional[str] = None,
        max_workers: int = 10,
    ):
        """
        初始化智能LLM搜索
        
        Args:
            model: LLM模型名称
            infer_type: 推理类型 (OpenAI, Google, local)
            engine: 搜索引擎 (google, baidu, bing)
            each_query_result: 每个查询的结果数量
            filter_date: 日期过滤器
            max_workers: 最大并发工作数
        """
        self.model = model
        self.infer_type = infer_type
        self.engine = engine
        self.each_query_result = each_query_result
        self.filter_date = filter_date
        self.max_workers = max_workers
        
        # 创建LLM对话代理
        self.conversation_agent = LLMConversationAgent(
            model=model,
            infer_type=infer_type
        )
        
        # 检查API密钥
        self._check_api_keys()
        
        logger.info(f"Intelligent LLM Search initialized with model={model}, engine={engine}")
    
    def _check_api_keys(self):
        """检查必要的API密钥"""
        # 检查LLM API密钥
        llm_keys = ["OPENAI_API_KEY", "GOOGLE_API_KEY"]
        llm_available = any(os.getenv(key) for key in llm_keys)
        
        # 检查搜索API密钥
        search_keys = ["SERP_API_KEY", "BING_SEARCH_V7_SUBSCRIPTION_KEY"]
        search_available = any(os.getenv(key) for key in search_keys)
        
        if not llm_available:
            logger.warning("No LLM API key found. Query generation may fail.")
        
        if not search_available:
            raise ValueError(
                "No valid search engine key provided. Please set SERP_API_KEY or BING_SEARCH_V7_SUBSCRIPTION_KEY."
            )
    
    def get_queries(self, topic: str, description: str = "") -> List[str]:
        """
        获取优化的搜索查询列表
        
        Args:
            topic: 研究主题
            description: 可选的主题描述/上下文
            
        Returns:
            list: 优化的搜索查询列表
        """
        try:
            # 构建任务描述
            task_description = f"""生成搜索查询任务：
主题: {topic}
描述: {description}
模型: {self.model}
要求: 生成优化的搜索查询列表，用于学术研究

请使用generate_search_queries工具，参数：
- topic: {topic}
- description: {description}
- model: {self.model}"""
            
            # 创建事件循环（如果不存在）
            try:
                loop = asyncio.get_event_loop()
            except RuntimeError:
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)
            
            # 执行智能搜索任务
            result = loop.run_until_complete(
                self.conversation_agent.execute_search_task(task_description)
            )
            
            if result["success"]:
                queries = result["tool_result"]["queries"]
                logger.info(f"Final count {len(queries)}:\n{queries}")
                return queries
            else:
                raise Exception(f"Search task failed: {result.get('error', 'Unknown error')}")
            
        except Exception as e:
            logger.error(f"Error generating queries: {e}")
            logger.error(traceback.format_exc())
            raise
    
    def web_search(self, query: str):
        """
        对单个查询执行网络搜索
        
        Args:
            query: 搜索查询字符串
            
        Returns:
            dict: 搜索结果字典，格式与原有接口一致
        """
        try:
            # 构建任务描述
            task_description = f"""单个查询搜索任务：
查询: {query}
引擎: {self.engine}
结果数量: {self.each_query_result}

请使用web_search工具，参数：
- queries: ["{query}"]
- topic: {query}
- top_n: {self.each_query_result}
- engine: {self.engine}"""
            
            # 创建事件循环（如果不存在）
            try:
                loop = asyncio.get_event_loop()
            except RuntimeError:
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)
            
            # 执行智能搜索任务
            result = loop.run_until_complete(
                self.conversation_agent.execute_search_task(task_description)
            )
            
            if result["success"]:
                # 转换为原有格式
                urls = result["tool_result"]["urls"]
                web_snippets = {}
                for idx, url in enumerate(urls):
                    web_snippets[idx] = {
                        "title": f"Result {idx + 1}",
                        "url": url,
                        "snippet": f"Search result for: {query}",
                    }
                
                return web_snippets
            else:
                raise Exception(f"Search task failed: {result.get('error', 'Unknown error')}")
            
        except Exception as e:
            logger.error(f"Error in web search for query '{query}': {e}")
            raise
    
    def batch_web_search(self, queries: List[str], topic: str, top_n: int = 20) -> List[str]:
        """
        批量执行网络搜索并返回按相关性过滤的结果
        
        Args:
            queries: 搜索查询列表
            topic: 主要主题，用于相关性过滤
            top_n: 返回的最相关URL数量
            
        Returns:
            list: 过滤后的最相关URL列表
        """
        try:
            # 构建任务描述
            queries_str = "\n".join([f"- {q}" for q in queries])
            task_description = f"""批量搜索任务：
主题: {topic}
查询列表:
{queries_str}
引擎: {self.engine}
最终结果数量: {top_n}

请使用web_search工具执行批量搜索，然后可选择性地使用analyze_search_results进行结果分析，参数：
- queries: {queries}
- topic: {topic}
- top_n: {top_n}
- engine: {self.engine}"""
            
            # 创建事件循环（如果不存在）
            try:
                loop = asyncio.get_event_loop()
            except RuntimeError:
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)
            
            # 执行智能搜索任务
            result = loop.run_until_complete(
                self.conversation_agent.execute_search_task(task_description)
            )
            
            if result["success"]:
                urls = result["tool_result"]["urls"]
                logger.info(f"Returning top {len(urls)} most relevant URLs.")
                return urls
            else:
                raise Exception(f"Search task failed: {result.get('error', 'Unknown error')}")
            
        except Exception as e:
            logger.error(f"Error in batch web search: {e}")
            logger.error(traceback.format_exc())
            raise
    
    def snippet_filter(self, topic: str, snippet: str) -> float:
        """
        计算主题和片段之间的相似度分数
        
        Args:
            topic: 搜索主题
            snippet: 要比较的文本片段
            
        Returns:
            float: 0到100之间的相似度分数
        """
        try:
            # 这里可以实现基于LLM的相似度计算
            # 可以通过LLM对话代理调用analyze_search_results工具
            logger.info(f"Calculating similarity for topic: {topic}")
            return 75.0  # 默认分数
            
        except Exception as e:
            logger.error(f"Error calculating similarity score: {e}")
            return 0.0
    
    def add_search_engine(self, name: str, config: dict):
        """
        添加新的搜索引擎
        
        Args:
            name: 搜索引擎名称
            config: 搜索引擎配置
        """
        self.conversation_agent.add_search_server(name, config)
        logger.info(f"Added new search engine: {name}")
    
    def list_available_engines(self) -> List[str]:
        """列出可用的搜索引擎"""
        return self.conversation_agent.list_available_servers()
    
    def crawl_urls(self, urls: List[str], topic: str = "") -> dict:
        """
        爬取URL列表并处理内容（新增功能）
        
        Args:
            urls: 要爬取的URL列表
            topic: 主题，用于内容过滤
            
        Returns:
            dict: 爬取结果
        """
        try:
            # 构建任务描述
            urls_str = "\n".join([f"- {url}" for url in urls])
            task_description = f"""网页爬取任务：
主题: {topic}
URL列表:
{urls_str}

请使用crawl_urls工具，参数：
- urls: {urls}
- topic: {topic}"""
            
            # 创建事件循环（如果不存在）
            try:
                loop = asyncio.get_event_loop()
            except RuntimeError:
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)
            
            # 执行智能搜索任务
            result = loop.run_until_complete(
                self.conversation_agent.execute_search_task(task_description)
            )
            
            if result["success"]:
                return result["tool_result"]
            else:
                raise Exception(f"Crawl task failed: {result.get('error', 'Unknown error')}")
            
        except Exception as e:
            logger.error(f"Error in URL crawling: {e}")
            raise

# 为了保持向后兼容性，提供一个工厂函数
def create_llm_search(
    model: str = "gemini-2.0-flash-thinking-exp-01-21",
    infer_type: str = "OpenAI",
    engine: Literal["google", "baidu", "bing"] = "google",
    each_query_result: int = 10,
    filter_date: Optional[str] = None,
    max_workers: int = 10,
) -> LLM_search:
    """
    创建智能LLM搜索实例的工厂函数
    
    Args:
        model: LLM模型名称
        infer_type: 推理类型
        engine: 搜索引擎
        each_query_result: 每个查询的结果数量
        filter_date: 日期过滤器
        max_workers: 最大并发工作数
        
    Returns:
        LLM_search: 智能LLM搜索实例
    """
    return LLM_search(
        model=model,
        infer_type=infer_type,
        engine=engine,
        each_query_result=each_query_result,
        filter_date=filter_date,
        max_workers=max_workers
    )
