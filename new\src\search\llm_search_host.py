import asyncio
import json
import logging
import traceback
from typing import List, Literal, Optional

from .llm_search_mcp_client import MC<PERSON><PERSON>, create_mcp_client_from_config

try:
    from ..request.wrapper import RequestWrapper
except ImportError:
    import sys
    import os
    sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..'))
    from request.wrapper import RequestWrapper

logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class LLM_search:
    
    def __init__(
        self,
        model: str = "gemini-2.0-flash-thinking-exp-01-21",
        infer_type: str = "OpenAI",
        max_workers: int = 10,
    ):
        self.request_wrapper = RequestWrapper(model=model, infer_type=infer_type)
        self._mcp_client: Optional[MCPClient] = None
    
    async def _get_mcp_client(self) -> MCPClient:
        if self._mcp_client is None or not self._mcp_client.is_connected:
            self._mcp_client = await create_mcp_client_from_config()
        return self._mcp_client
    
    async def _cleanup_client(self):
        if self._mcp_client and self._mcp_client.is_connected:
            await self._mcp_client.disconnect()
            self._mcp_client = None
    
    async def _get_available_tools(self) -> List[dict]:
        client = await self._get_mcp_client()
        return await client.list_tools()
    
    def _create_tool_selection_prompt(self, task_description: str, available_tools: List[dict]) -> str:
        tools_info = "\n".join([
            f"- {tool['name']}: {tool['description']}"
            for tool in available_tools
        ])
        
        return f"""你是一个搜索智能体。用户给你一个搜索任务，你需要选择最合适的工具来完成。

可用工具：
{tools_info}

用户任务：{task_description}

请分析任务需求，选择最合适的工具，并提供调用参数。注意：不要在参数中包含配置信息（如engine、model等），这些由Server自动处理。

请按以下JSON格式返回：
{{
    "selected_tool": "工具名称",
    "arguments": {{
        "参数名": "参数值"
    }},
    "reasoning": "选择理由"
}}"""

    async def _llm_select_and_call_tool(self, task_description: str, **user_args) -> dict:
        try:
            available_tools = await self._get_available_tools()
            
            prompt = self._create_tool_selection_prompt(task_description, available_tools)
            response = await self.request_wrapper.async_request(
                messages=[{"role": "user", "content": prompt}],
                temperature=0.1,
                max_tokens=1000
            )
            
            try:
                if "```json" in response:
                    json_start = response.find("```json") + 7
                    json_end = response.find("```", json_start)
                    json_text = response[json_start:json_end].strip()
                else:
                    json_text = response.strip()
                
                decision = json.loads(json_text)
                
                arguments = decision["arguments"]
                arguments.update(user_args)
                
                client = await self._get_mcp_client()
                result = await client.call_tool(decision["selected_tool"], arguments)
                
                logger.info(f"LLM selected tool: {decision['selected_tool']}, reasoning: {decision.get('reasoning', 'N/A')}")
                return result
                
            except json.JSONDecodeError as e:
                logger.error(f"Failed to parse LLM response: {e}")
                client = await self._get_mcp_client()
                return await client.call_tool("generate_search_queries", user_args)
                
        except Exception as e:
            logger.error(f"Error in LLM tool selection: {e}")
            raise
        finally:
            await self._cleanup_client()

def create_llm_search(
    model: str = "gemini-2.0-flash-thinking-exp-01-21",
    infer_type: str = "OpenAI",
    max_workers: int = 10,
) -> LLM_search:
    return LLM_search(
        model=model,
        infer_type=infer_type,
        max_workers=max_workers
    )
