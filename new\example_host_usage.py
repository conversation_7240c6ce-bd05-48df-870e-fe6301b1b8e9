#!/usr/bin/env python3
"""
LLM Search Host 使用示例
演示如何使用简化版本和复杂版本
"""

import sys
import os
import time

# 添加项目路径
sys.path.append(os.path.join(os.path.dirname(__file__), '.'))

def example_simple_host():
    """简化版本使用示例"""
    print("\n" + "="*60)
    print("简化版本 (SimpleLLMSearch) 使用示例")
    print("="*60)
    
    try:
        from src.search.llm_search_simple_host import SimpleLLMSearch
        
        # 1. 创建实例
        print("1. 创建SimpleLLMSearch实例...")
        llm_search = SimpleLLMSearch(
            model="gemini-2.0-flash-thinking-exp-01-21",
            engine="google",
            each_query_result=5
        )
        print("✓ 实例创建成功")
        
        # 2. 生成搜索查询
        print("\n2. 生成搜索查询...")
        start_time = time.time()
        queries = llm_search.get_queries(
            topic="人工智能伦理",
            description="研究人工智能发展中的伦理问题和挑战"
        )
        end_time = time.time()
        
        print(f"✓ 生成了 {len(queries)} 个查询，耗时 {end_time-start_time:.2f}秒")
        for i, query in enumerate(queries[:3], 1):
            print(f"   {i}. {query}")
        if len(queries) > 3:
            print(f"   ... 还有 {len(queries)-3} 个查询")
        
        # 3. 执行单个搜索
        print("\n3. 执行单个搜索...")
        start_time = time.time()
        search_results = llm_search.web_search("AI伦理准则")
        end_time = time.time()
        
        print(f"✓ 搜索完成，找到 {len(search_results)} 个结果，耗时 {end_time-start_time:.2f}秒")
        for i, (idx, result) in enumerate(list(search_results.items())[:2]):
            print(f"   {i+1}. {result['title']}")
            print(f"      URL: {result['url'][:60]}...")
        
        # 4. 批量搜索
        print("\n4. 执行批量搜索...")
        start_time = time.time()
        urls = llm_search.batch_web_search(
            queries=queries[:2],  # 只用前2个查询做演示
            topic="人工智能伦理",
            top_n=8
        )
        end_time = time.time()
        
        print(f"✓ 批量搜索完成，找到 {len(urls)} 个相关URL，耗时 {end_time-start_time:.2f}秒")
        for i, url in enumerate(urls[:3], 1):
            print(f"   {i}. {url[:60]}...")
        
        print("\n✅ 简化版本演示完成")
        return True
        
    except Exception as e:
        print(f"❌ 简化版本演示失败: {e}")
        return False

def example_complex_host():
    """复杂版本使用示例"""
    print("\n" + "="*60)
    print("复杂版本 (LLM_search) 使用示例")
    print("="*60)
    
    try:
        from src.search.llm_search_host import LLM_search
        
        # 1. 创建实例
        print("1. 创建LLM_search实例...")
        llm_search = LLM_search(
            model="gemini-2.0-flash-thinking-exp-01-21",
            engine="google",
            each_query_result=5
        )
        print("✓ 实例创建成功")
        
        # 2. 生成搜索查询（AI智能分析）
        print("\n2. AI智能生成搜索查询...")
        start_time = time.time()
        queries = llm_search.get_queries(
            topic="量子计算",
            description="研究量子计算的最新进展、算法和实际应用"
        )
        end_time = time.time()
        
        print(f"✓ AI生成了 {len(queries)} 个查询，耗时 {end_time-start_time:.2f}秒")
        for i, query in enumerate(queries[:3], 1):
            print(f"   {i}. {query}")
        if len(queries) > 3:
            print(f"   ... 还有 {len(queries)-3} 个查询")
        
        # 3. 执行单个搜索（AI选择策略）
        print("\n3. AI智能执行搜索...")
        start_time = time.time()
        search_results = llm_search.web_search("量子算法优化")
        end_time = time.time()
        
        print(f"✓ AI搜索完成，找到 {len(search_results)} 个结果，耗时 {end_time-start_time:.2f}秒")
        for i, (idx, result) in enumerate(list(search_results.items())[:2]):
            print(f"   {i+1}. {result['title']}")
            print(f"      URL: {result['url'][:60]}...")
        
        # 4. 批量搜索（AI优化策略）
        print("\n4. AI智能批量搜索...")
        start_time = time.time()
        urls = llm_search.batch_web_search(
            queries=queries[:2],  # 只用前2个查询做演示
            topic="量子计算",
            top_n=8
        )
        end_time = time.time()
        
        print(f"✓ AI批量搜索完成，找到 {len(urls)} 个相关URL，耗时 {end_time-start_time:.2f}秒")
        for i, url in enumerate(urls[:3], 1):
            print(f"   {i}. {url[:60]}...")
        
        print("\n✅ 复杂版本演示完成")
        return True
        
    except Exception as e:
        print(f"❌ 复杂版本演示失败: {e}")
        return False

def example_practical_usage():
    """实际使用场景示例"""
    print("\n" + "="*60)
    print("实际使用场景示例")
    print("="*60)
    
    # 场景1：学术研究（推荐复杂版本）
    print("\n📚 场景1：学术研究 - 使用复杂版本")
    try:
        from src.search.llm_search_host import LLM_search
        
        llm_search = LLM_search()
        
        # 研究深度学习的最新进展
        queries = llm_search.get_queries(
            topic="Transformer架构改进",
            description="研究Transformer模型的最新改进和优化方法"
        )
        
        print(f"✓ 为学术研究生成了 {len(queries)} 个专业查询")
        print(f"  示例: {queries[0] if queries else 'N/A'}")
        
    except Exception as e:
        print(f"❌ 学术研究场景失败: {e}")
    
    # 场景2：生产环境（推荐简化版本）
    print("\n🏭 场景2：生产环境 - 使用简化版本")
    try:
        from src.search.llm_search_simple_host import SimpleLLMSearch
        
        llm_search = SimpleLLMSearch()
        
        # 快速搜索产品信息
        queries = llm_search.get_queries(
            topic="云计算服务",
            description="云计算平台的功能和价格对比"
        )
        
        print(f"✓ 为生产环境快速生成了 {len(queries)} 个查询")
        print(f"  示例: {queries[0] if queries else 'N/A'}")
        
    except Exception as e:
        print(f"❌ 生产环境场景失败: {e}")

def example_migration_guide():
    """迁移指南示例"""
    print("\n" + "="*60)
    print("从原有LLM_search迁移示例")
    print("="*60)
    
    print("""
# 原有代码
from LLM_search import LLM_search

llm_search = LLM_search(
    model="gpt-3.5-turbo",
    engine="google",
    each_query_result=10
)

queries = llm_search.get_queries("机器学习")
results = llm_search.batch_web_search(queries, "机器学习", 20)

# 迁移方案1：使用简化版本（推荐用于生产环境）
from src.search.llm_search_simple_host import SimpleLLMSearch

llm_search = SimpleLLMSearch(
    model="gemini-2.0-flash-thinking-exp-01-21",  # 更新模型
    engine="google",
    each_query_result=10
)

# API完全兼容，无需修改
queries = llm_search.get_queries("机器学习")
results = llm_search.batch_web_search(queries, "机器学习", 20)

# 迁移方案2：使用复杂版本（推荐用于研究环境）
from src.search.llm_search_host import LLM_search

llm_search = LLM_search(
    model="gemini-2.0-flash-thinking-exp-01-21",  # 更新模型
    engine="google",
    each_query_result=10
)

# API完全兼容，但内部会有AI智能决策
queries = llm_search.get_queries("机器学习")
results = llm_search.batch_web_search(queries, "机器学习", 20)
    """)

def print_usage_summary():
    """打印使用总结"""
    print("\n" + "="*60)
    print("使用总结")
    print("="*60)
    print("""
🎯 选择指南：

1. 生产环境 → SimpleLLMSearch
   • 性能优先
   • 稳定可靠
   • 资源消耗少

2. 研究环境 → LLM_search
   • AI智能决策
   • 复杂任务处理
   • 高扩展性

📝 基本用法：

# 创建实例
llm_search = SimpleLLMSearch()  # 或 LLM_search()

# 生成查询
queries = llm_search.get_queries("主题", "描述")

# 执行搜索
results = llm_search.batch_web_search(queries, "主题", 20)

# 爬取内容
content = llm_search.crawl_urls(urls, "主题")

🔧 配置要求：

export OPENAI_API_KEY="your_key"
export SERP_API_KEY="your_key"

💡 最佳实践：

• 使用批量搜索而不是单个搜索
• 设置合适的结果数量限制
• 在生产环境使用简化版本
• 在研究环境使用复杂版本
    """)

def main():
    """主函数"""
    print("LLM Search Host 使用示例演示")
    print("本演示将展示如何使用两种不同的Host实现")
    
    # 运行示例
    simple_success = example_simple_host()
    complex_success = example_complex_host()
    
    # 实际使用场景
    example_practical_usage()
    
    # 迁移指南
    example_migration_guide()
    
    # 使用总结
    print_usage_summary()
    
    # 总结
    print("\n" + "="*60)
    print("演示总结")
    print("="*60)
    
    if simple_success and complex_success:
        print("✅ 两种版本都运行成功！")
        print("你可以根据具体需求选择合适的版本。")
    elif simple_success:
        print("✅ 简化版本运行成功！")
        print("❌ 复杂版本运行失败，建议检查配置。")
    elif complex_success:
        print("❌ 简化版本运行失败，建议检查配置。")
        print("✅ 复杂版本运行成功！")
    else:
        print("❌ 两种版本都运行失败，请检查：")
        print("   1. API密钥是否正确设置")
        print("   2. 网络连接是否正常")
        print("   3. 依赖包是否正确安装")
    
    print("\n演示完成！")

if __name__ == "__main__":
    main()
